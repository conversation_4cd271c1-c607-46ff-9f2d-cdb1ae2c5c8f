<div class="outer-body">
  <div
    *ngIf="_internalVisible"
    class="awe-modal-backdrop"
    [ngClass]="[
      backdropClass,
      animation !== 'none' ? 'animating' : '',
      isOpen && !isAnimatingOut ? 'awe-modal-open' : '',
      isAnimatingOut ? 'awe-modal-closing' : '',
    ]"
    (click)="onBackdropClicked($event)"
  >
    <div
      class="awe-modal-content"
      role="dialog"
      aria-modal="true"
      [attr.aria-labelledby]="showHeader && title ? 'awe-modal-title' : null"
      [ngClass]="[
        modalClass,
        'position-' + position,
        animation !== 'none' ? 'animation-' + animation : '',
      ]"
      [style.width]="width"
      [style.height]="height"
      [style.max-width]="maxWidth"
      [style.max-height]="maxHeight"
      (click)="$event.stopPropagation()"
    >
      <!-- Header -->
      <div *ngIf="showHeader" class="awe-modal-header">
        <ng-content select="[awe-modal-header]"></ng-content>
        <!-- Default title and close button if no header content projected or if explicitly wanted -->
        <h2
          *ngIf="title && !hasProjectedHeaderContent()"
          id="awe-modal-title"
          class="awe-modal-default-title"
        >
          {{ title }}
        </h2>
        <awe-icons
          *ngIf="showCloseButton"
          type="button"
          class="awe-modal-close-button"
          (click)="closeModal()"
          aria-label="Close modal"
          iconName="awe_close"
          color="#6c757d"
        ></awe-icons>
      </div>

      <!-- Body -->
      <div class="awe-modal-body">
        <ng-content></ng-content>
        <!-- Default slot for body -->
        <ng-content select="[awe-modal-body]"></ng-content>
        <!-- Named slot for body too -->
      </div>

      <!-- Footer -->
      <div *ngIf="showFooter" class="awe-modal-footer">
        <ng-content select="[awe-modal-footer]"></ng-content>
      </div>
    </div>
  </div>
</div>
