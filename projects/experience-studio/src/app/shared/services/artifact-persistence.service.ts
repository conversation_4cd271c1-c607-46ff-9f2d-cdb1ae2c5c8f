import { Injectable, DestroyRef, inject, signal, computed } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map, distinctUntilChanged } from 'rxjs/operators';

/**
 * Interface for artifact data structure
 */
export interface ArtifactData {
  id: string;
  name: string;
  type: 'readme' | 'layout' | 'design-tokens' | 'logs';
  content: any;
  timestamp: number;
  persistent: boolean;
}

/**
 * Interface for layout analyzed data
 */
export interface LayoutAnalyzedData {
  key: string;
  name: string;
  imageUrl: string;
}

/**
 * Interface for design system data
 */
export interface DesignSystemData {
  colors?: any[];
  typography?: any;
  spacing?: any;
  shadows?: any;
  [key: string]: any;
}

/**
 * Interface for artifact persistence state
 */
export interface ArtifactPersistenceState {
  artifacts: ArtifactData[];
  layoutAnalyzed: LayoutAnalyzedData[];
  designSystem: DesignSystemData | null;
  hasLayoutAnalyzed: boolean;
  hasDesignSystem: boolean;
  isLoading: boolean;
  error: string | null;
}

/**
 * Enhanced artifact persistence service using Angular 19+ patterns
 * Manages artifact data persistence using BehaviorSubject state management
 * Ensures data survives FAILED SSE events and component reinitialization
 */
@Injectable({
  providedIn: 'root'
})
export class ArtifactPersistenceService {
  private readonly destroyRef = inject(DestroyRef);

  // Primary state management using BehaviorSubjects
  private readonly artifactsSubject = new BehaviorSubject<ArtifactData[]>([]);
  private readonly layoutAnalyzedSubject = new BehaviorSubject<LayoutAnalyzedData[]>([]);
  private readonly designSystemSubject = new BehaviorSubject<DesignSystemData | null>(null);
  private readonly hasLayoutAnalyzedSubject = new BehaviorSubject<boolean>(false);
  private readonly hasDesignSystemSubject = new BehaviorSubject<boolean>(false);
  private readonly isLoadingSubject = new BehaviorSubject<boolean>(false);
  private readonly errorSubject = new BehaviorSubject<string | null>(null);

  // Angular 19+ Signals for reactive state
  private readonly artifactsSignal = signal<ArtifactData[]>([]);
  private readonly layoutAnalyzedSignal = signal<LayoutAnalyzedData[]>([]);
  private readonly designSystemSignal = signal<DesignSystemData | null>(null);
  private readonly hasLayoutAnalyzedSignal = signal<boolean>(false);
  private readonly hasDesignSystemSignal = signal<boolean>(false);
  private readonly isLoadingSignal = signal<boolean>(false);
  private readonly errorSignal = signal<string | null>(null);

  // Computed signals for derived state
  public readonly artifactCount = computed(() => this.artifactsSignal().length);
  public readonly hasAnyArtifacts = computed(() => this.artifactsSignal().length > 0);
  public readonly hasLayoutData = computed(() => this.layoutAnalyzedSignal().length > 0);
  public readonly hasDesignData = computed(() => this.designSystemSignal() !== null);

  // Observable streams for reactive programming
  public readonly artifacts$ = this.artifactsSubject.asObservable();
  public readonly layoutAnalyzed$ = this.layoutAnalyzedSubject.asObservable();
  public readonly designSystem$ = this.designSystemSubject.asObservable();
  public readonly hasLayoutAnalyzed$ = this.hasLayoutAnalyzedSubject.asObservable();
  public readonly hasDesignSystem$ = this.hasDesignSystemSubject.asObservable();
  public readonly isLoading$ = this.isLoadingSubject.asObservable();
  public readonly error$ = this.errorSubject.asObservable();

  // Combined state observable
  public readonly state$: Observable<ArtifactPersistenceState> = combineLatest([
    this.artifacts$,
    this.layoutAnalyzed$,
    this.designSystem$,
    this.hasLayoutAnalyzed$,
    this.hasDesignSystem$,
    this.isLoading$,
    this.error$
  ]).pipe(
    map(([artifacts, layoutAnalyzed, designSystem, hasLayoutAnalyzed, hasDesignSystem, isLoading, error]) => ({
      artifacts,
      layoutAnalyzed,
      designSystem,
      hasLayoutAnalyzed,
      hasDesignSystem,
      isLoading,
      error
    })),
    distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)),
    takeUntilDestroyed(this.destroyRef)
  );

  // In-memory persistence map for backup storage
  private readonly persistentArtifacts = new Map<string, any>();

  constructor() {
    this.initializeSignalSync();
    console.log('🛡️ ArtifactPersistenceService initialized with Angular 19+ patterns');
  }

  /**
   * Initialize synchronization between BehaviorSubjects and Signals
   */
  private initializeSignalSync(): void {
    // Sync BehaviorSubjects to Signals
    this.artifacts$.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(artifacts => this.artifactsSignal.set(artifacts));

    this.layoutAnalyzed$.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(layoutAnalyzed => this.layoutAnalyzedSignal.set(layoutAnalyzed));

    this.designSystem$.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(designSystem => this.designSystemSignal.set(designSystem));

    this.hasLayoutAnalyzed$.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(hasLayoutAnalyzed => this.hasLayoutAnalyzedSignal.set(hasLayoutAnalyzed));

    this.hasDesignSystem$.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(hasDesignSystem => this.hasDesignSystemSignal.set(hasDesignSystem));

    this.isLoading$.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(isLoading => this.isLoadingSignal.set(isLoading));

    this.error$.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(error => this.errorSignal.set(error));
  }

  /**
   * Store artifact data persistently
   */
  public storeArtifact(type: 'layout' | 'design-tokens' | 'readme', artifactData: any): void {
    console.log('🛡️ Storing persistent artifact:', type, artifactData);

    const timestamp = Date.now();
    const persistentData = {
      ...artifactData,
      timestamp,
      persistent: true
    };

    // Store in backup Map
    this.persistentArtifacts.set(type, persistentData);

    // Update appropriate state based on type
    switch (type) {
      case 'layout':
        this.storeLayoutArtifact(persistentData);
        break;
      case 'design-tokens':
        this.storeDesignSystemArtifact(persistentData);
        break;
      case 'readme':
        this.storeReadmeArtifact(persistentData);
        break;
    }

    console.log('✅ Artifact stored successfully:', type);
    console.log('📊 Total persistent artifacts:', this.persistentArtifacts.size);
  }

  /**
   * Store layout artifact data
   */
  private storeLayoutArtifact(artifactData: any): void {
    // Create layout analyzed data
    const layoutData: LayoutAnalyzedData = {
      key: artifactData.layoutKey || 'HB',
      name: artifactData.layoutName || 'Header + Body',
      imageUrl: artifactData.imageUrl || `assets/images/layout-${artifactData.layoutKey || 'HB'}.png`
    };

    this.layoutAnalyzedSubject.next([layoutData]);
    this.hasLayoutAnalyzedSubject.next(true);

    // Add to artifacts list
    this.addToArtifactsList({
      id: `layout-${Date.now()}`,
      name: 'Layout Analyzed',
      type: 'layout',
      content: layoutData.imageUrl,
      timestamp: Date.now(),
      persistent: true
    });
  }

  /**
   * Store design system artifact data
   */
  private storeDesignSystemArtifact(artifactData: any): void {
    const designSystemData = artifactData.tokens || artifactData;

    this.designSystemSubject.next(designSystemData);
    this.hasDesignSystemSubject.next(true);

    // Add to artifacts list
    this.addToArtifactsList({
      id: `design-system-${Date.now()}`,
      name: 'Design System',
      type: 'design-tokens',
      content: designSystemData,
      timestamp: Date.now(),
      persistent: true
    });
  }

  /**
   * Store readme artifact data
   */
  private storeReadmeArtifact(artifactData: any): void {
    this.addToArtifactsList({
      id: `readme-${Date.now()}`,
      name: 'Project Overview',
      type: 'readme',
      content: artifactData.content || artifactData,
      timestamp: Date.now(),
      persistent: true
    });
  }

  /**
   * Add artifact to the artifacts list
   */
  private addToArtifactsList(artifact: ArtifactData): void {
    const currentArtifacts = this.artifactsSubject.value;

    // Remove existing artifact of the same type to prevent duplicates
    const filteredArtifacts = currentArtifacts.filter(a => a.name !== artifact.name);

    // Add new artifact
    const updatedArtifacts = [...filteredArtifacts, artifact];

    this.artifactsSubject.next(updatedArtifacts);
  }

  /**
   * Restore all persistent artifacts (called during FAILED events)
   * ENHANCED: Robust error recovery with fallback mechanisms
   */
  public restorePersistentArtifacts(): void {
    console.log('🔄 Restoring persistent artifacts from in-memory storage');
    console.log('📊 Persistent artifacts count:', this.persistentArtifacts.size);

    if (this.persistentArtifacts.size === 0) {
      console.warn('⚠️ No persistent artifacts available to restore');
      this.setError('No persistent artifacts available for recovery');
      return;
    }

    try {
      this.setLoading(true);
      this.clearError();

      let restoredCount = 0;
      const totalArtifacts = this.persistentArtifacts.size;

      for (const [key, artifactData] of this.persistentArtifacts.entries()) {
        console.log('🛡️ Restoring persistent artifact:', key);

        try {
          switch (key) {
            case 'layout':
              this.storeLayoutArtifact(artifactData);
              console.log('✅ Layout analyzed artifact restored');
              restoredCount++;
              break;
            case 'design-tokens':
              this.storeDesignSystemArtifact(artifactData);
              console.log('✅ Design system artifact restored');
              restoredCount++;
              break;
            case 'readme':
              this.storeReadmeArtifact(artifactData);
              console.log('✅ Project overview artifact restored');
              restoredCount++;
              break;
            default:
              console.log('⚠️ Unknown persistent artifact type:', key);
          }
        } catch (error) {
          console.error('❌ Failed to restore artifact:', key, error);
          // Continue with other artifacts even if one fails
        }
      }

      this.setLoading(false);

      if (restoredCount === totalArtifacts) {
        console.log('✅ All persistent artifacts restored successfully');
      } else {
        console.warn(`⚠️ Partially restored artifacts: ${restoredCount}/${totalArtifacts}`);
        this.setError(`Partially restored artifacts: ${restoredCount}/${totalArtifacts}`);
      }
    } catch (error) {
      console.error('❌ Critical error during artifact restoration:', error);
      this.setError('Critical error during artifact restoration');
      this.setLoading(false);
    }
  }

  /**
   * Handle FAILED SSE events with enhanced recovery
   * ENHANCED: Specific handling for FAILED events with comprehensive recovery
   */
  public handleFailedEvent(eventData?: any): void {
    console.log('🚨 Handling FAILED SSE event with enhanced recovery');

    try {
      // Log event details for debugging
      if (eventData) {
        console.log('📊 FAILED event data:', {
          status: eventData.status,
          progress: eventData.progress,
          errorMessage: eventData.errorMessage || eventData.log || 'Unknown error'
        });
      }

      // Set error state with specific FAILED event information
      const errorMessage = eventData?.errorMessage || eventData?.log || 'Operation failed - restoring previous data';
      this.setError(errorMessage);

      // Attempt to restore persistent artifacts
      this.restorePersistentArtifacts();

      // Verify restoration was successful
      const hasArtifacts = this.hasAnyArtifacts();
      const hasLayout = this.hasLayoutData();
      const hasDesign = this.hasDesignData();

      console.log('🔍 Post-restoration state:', {
        hasArtifacts,
        hasLayout,
        hasDesign,
        artifactCount: this.artifactCount()
      });

      // If restoration was successful, clear the error
      if (hasArtifacts) {
        console.log('✅ FAILED event recovery successful - artifacts preserved');
        this.clearError();
      } else {
        console.warn('⚠️ FAILED event recovery incomplete - no artifacts available');
      }

    } catch (error) {
      console.error('❌ Critical error during FAILED event handling:', error);
      this.setError('Critical error during FAILED event recovery');
    }
  }

  /**
   * Handle specific BUILD FAILED scenarios
   * ENHANCED: Comprehensive BUILD failure handling
   */
  public handleBuildFailed(errorData: { message?: string; log?: string }): void {
    console.log('🔨 Handling BUILD FAILED scenario');
    this.handleFailedEvent({
      status: 'FAILED',
      progress: 'BUILD',
      errorMessage: errorData.message || errorData.log || 'Build process failed'
    });
  }

  /**
   * Handle specific DEPLOY FAILED scenarios
   * ENHANCED: Comprehensive DEPLOY failure handling
   */
  public handleDeployFailed(errorData: { message?: string; log?: string }): void {
    console.log('🚀 Handling DEPLOY FAILED scenario');
    this.handleFailedEvent({
      status: 'FAILED',
      progress: 'DEPLOY',
      errorMessage: errorData.message || errorData.log || 'Deployment process failed'
    });
  }

  /**
   * Handle network/connection FAILED scenarios
   * ENHANCED: Comprehensive NETWORK failure handling
   */
  public handleNetworkFailed(errorData: { message?: string; error?: any }): void {
    console.log('🌐 Handling NETWORK FAILED scenario');
    this.handleFailedEvent({
      status: 'FAILED',
      progress: 'NETWORK',
      errorMessage: errorData.message || 'Network connection failed'
    });
  }

  /**
   * Handle regeneration FAILED scenarios
   * ENHANCED: Comprehensive REGENERATION failure handling
   */
  public handleRegenerationFailed(errorData: { message?: string; log?: string }): void {
    console.log('🔄 Handling REGENERATION FAILED scenario');
    this.handleFailedEvent({
      status: 'FAILED',
      progress: 'REGENERATION',
      errorMessage: errorData.message || errorData.log || 'Regeneration process failed'
    });
  }

  /**
   * Clear error state
   */
  public clearError(): void {
    this.errorSubject.next(null);
  }

  /**
   * Set loading state
   */
  public setLoading(loading: boolean): void {
    this.isLoadingSubject.next(loading);
  }

  /**
   * Set error state
   */
  public setError(error: string): void {
    this.errorSubject.next(error);
  }

  /**
   * Get current artifacts data (for backward compatibility)
   */
  public getCurrentArtifacts(): ArtifactData[] {
    return this.artifactsSubject.value;
  }

  /**
   * Get current layout analyzed data (for backward compatibility)
   */
  public getCurrentLayoutAnalyzed(): LayoutAnalyzedData[] {
    return this.layoutAnalyzedSubject.value;
  }

  /**
   * Get current design system data (for backward compatibility)
   */
  public getCurrentDesignSystem(): DesignSystemData | null {
    return this.designSystemSubject.value;
  }

  /**
   * Check if has layout analyzed data
   */
  public getHasLayoutAnalyzed(): boolean {
    return this.hasLayoutAnalyzedSubject.value;
  }

  /**
   * Check if has design system data
   */
  public getHasDesignSystem(): boolean {
    return this.hasDesignSystemSubject.value;
  }

  /**
   * Reset all state (for navigation cleanup)
   */
  public resetState(): void {
    console.log('🔄 Resetting artifact persistence service state');

    this.artifactsSubject.next([]);
    this.layoutAnalyzedSubject.next([]);
    this.designSystemSubject.next(null);
    this.hasLayoutAnalyzedSubject.next(false);
    this.hasDesignSystemSubject.next(false);
    this.isLoadingSubject.next(false);
    this.errorSubject.next(null);

    // Clear persistent artifacts map
    this.persistentArtifacts.clear();

    console.log('✅ Artifact persistence service state reset completed');
  }

  /**
   * Initialize service and restore any existing persistent artifacts
   * Called during component initialization to ensure data is available
   */
  public initializeAndRestore(): void {
    console.log('🚀 Initializing artifact persistence service');

    // Restore any existing persistent artifacts
    if (this.persistentArtifacts.size > 0) {
      console.log('🛡️ Restoring existing persistent artifacts during initialization');
      this.restorePersistentArtifacts();
    } else {
      console.log('ℹ️ No persistent artifacts found during initialization');
    }

    console.log('✅ Artifact persistence service initialization completed');
  }

  /**
   * Reset state with persistence (preserves persistent artifacts)
   */
  public resetStateWithPersistence(): void {
    console.log('🔄 Resetting artifact state with persistence preservation');

    // Clear current state but preserve persistent artifacts
    this.artifactsSubject.next([]);
    this.layoutAnalyzedSubject.next([]);
    this.designSystemSubject.next(null);
    this.hasLayoutAnalyzedSubject.next(false);
    this.hasDesignSystemSubject.next(false);
    this.isLoadingSubject.next(false);
    this.errorSubject.next(null);

    // Restore persistent artifacts immediately
    if (this.persistentArtifacts.size > 0) {
      console.log('🛡️ Restoring persistent artifacts after state reset');
      this.restorePersistentArtifacts();
    }

    console.log('✅ Artifact state reset with persistence completed');
  }
}
