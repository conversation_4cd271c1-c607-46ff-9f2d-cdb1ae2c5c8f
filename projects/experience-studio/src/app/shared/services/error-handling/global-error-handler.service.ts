import { Injectable } from '@angular/core';
import { HttpErrorResponse, HttpRequest } from '@angular/common/http';
import { ToastService } from '../toast.service';
import { EnvironmentValidationError } from '../environment-validation.service';
import { createLogger } from '../../utils/logger';

/**
 * Standardized error response format
 */
export interface StandardErrorResponse {
  status: number;
  statusText: string;
  message: string;
  details?: any;
  timestamp: string;
  url: string;
  method: string;
}

/**
 * Error handling configuration
 */
export interface ErrorHandlingConfig {
  showToast: boolean;
  logError: boolean;
  retryable: boolean;
  userFriendlyMessage?: string;
}

/**
 * Global Error Handler Service
 * Centralized error handling for HTTP requests and application errors
 * Replaces duplicated error handling patterns across services
 */
@Injectable({
  providedIn: 'root'
})
export class GlobalErrorHandlerService {
  private logger = createLogger('GlobalErrorHandlerService');

  // Default error handling configurations for different error types
  private readonly defaultConfigs: { [key: number]: ErrorHandlingConfig } = {
    400: { showToast: true, logError: true, retryable: false, userFriendlyMessage: 'Invalid request. Please check your input and try again.' },
    401: { showToast: true, logError: true, retryable: false, userFriendlyMessage: 'Authentication required. Please log in and try again.' },
    403: { showToast: true, logError: true, retryable: false, userFriendlyMessage: 'Access denied. You don\'t have permission to perform this action.' },
    404: { showToast: true, logError: true, retryable: false, userFriendlyMessage: 'Resource not found. The requested item may have been moved or deleted.' },
    408: { showToast: true, logError: true, retryable: true, userFriendlyMessage: 'Request timeout. Please try again.' },
    429: { showToast: true, logError: true, retryable: true, userFriendlyMessage: 'Too many requests. Please wait a moment and try again.' },
    500: { showToast: true, logError: true, retryable: true, userFriendlyMessage: 'Server error. Please try again later.' },
    502: { showToast: true, logError: true, retryable: true, userFriendlyMessage: 'Service temporarily unavailable. Please try again later.' },
    503: { showToast: true, logError: true, retryable: true, userFriendlyMessage: 'Service temporarily unavailable. Please try again later.' },
    504: { showToast: true, logError: true, retryable: true, userFriendlyMessage: 'Request timeout. Please try again.' }
  };

  constructor(private toastService: ToastService) {}

  /**
   * Handle HTTP errors with standardized response format
   * @param error The HTTP error response
   * @param request The original HTTP request
   * @param customConfig Optional custom error handling configuration
   * @returns Standardized error response
   */
  handleHttpError(
    error: HttpErrorResponse,
    request: HttpRequest<any>,
    customConfig?: Partial<ErrorHandlingConfig>
  ): StandardErrorResponse {
    // Create standardized error response
    const standardError: StandardErrorResponse = this.createStandardErrorResponse(error, request);

    // Get error handling configuration
    const config = this.getErrorConfig(error.status, customConfig);

    // Log error if configured
    if (config.logError) {
      // this.logger.error('HTTP Error:', standardError);
    }

    // Show user-friendly toast if configured
    if (config.showToast) {
      this.showErrorToast(standardError, config);
    }

    return standardError;
  }

  /**
   * Handle application errors (non-HTTP)
   * @param error The error object
   * @param context Additional context information
   * @param customConfig Optional custom error handling configuration
   */
  handleApplicationError(
    error: any,
    context: string,
    customConfig?: Partial<ErrorHandlingConfig>
  ): void {
    const config = { showToast: true, logError: true, retryable: false, ...customConfig };

    // Log error if configured
    if (config.logError) {
      this.logger.error(`Application Error in ${context}:`, error);
    }

    // Show user-friendly toast if configured
    if (config.showToast) {
      const message = config.userFriendlyMessage || 'An unexpected error occurred. Please try again.';
      this.toastService.error(message);
    }
  }

  /**
   * Create standardized error response format
   * @param error The HTTP error response
   * @param request The original HTTP request
   * @returns Standardized error response
   */
  private createStandardErrorResponse(
    error: HttpErrorResponse,
    request: HttpRequest<any>
  ): StandardErrorResponse {
    return {
      status: error.status,
      statusText: error.statusText,
      message: this.extractErrorMessage(error),
      details: error.error,
      timestamp: new Date().toISOString(),
      url: request.url,
      method: request.method
    };
  }

  /**
   * Extract meaningful error message from HTTP error response
   * @param error The HTTP error response
   * @returns Extracted error message
   */
  private extractErrorMessage(error: HttpErrorResponse): string {
    if (error.error?.message) {
      return error.error.message;
    }
    if (error.error?.error) {
      return error.error.error;
    }
    if (typeof error.error === 'string') {
      return error.error;
    }
    return error.message || `HTTP ${error.status} Error`;
  }

  /**
   * Get error handling configuration for specific error status
   * @param status HTTP status code
   * @param customConfig Optional custom configuration
   * @returns Error handling configuration
   */
  private getErrorConfig(
    status: number,
    customConfig?: Partial<ErrorHandlingConfig>
  ): ErrorHandlingConfig {
    const defaultConfig = this.defaultConfigs[status] || {
      showToast: true,
      logError: true,
      retryable: false,
      userFriendlyMessage: 'An error occurred. Please try again.'
    };

    return { ...defaultConfig, ...customConfig };
  }

  /**
   * Show error toast with appropriate message
   * @param error Standardized error response
   * @param config Error handling configuration
   */
  private showErrorToast(error: StandardErrorResponse, config: ErrorHandlingConfig): void {
    const message = config.userFriendlyMessage || error.message;
    this.toastService.error(message);
  }

  /**
   * Check if an error is retryable based on status code
   * @param status HTTP status code
   * @returns Whether the error is retryable
   */
  isRetryable(status: number): boolean {
    const config = this.defaultConfigs[status];
    return config?.retryable || false;
  }

  /**
   * Get user-friendly error message for status code
   * @param status HTTP status code
   * @returns User-friendly error message
   */
  getUserFriendlyMessage(status: number): string {
    const config = this.defaultConfigs[status];
    return config?.userFriendlyMessage || 'An error occurred. Please try again.';
  }

  /**
   * Handle environment validation errors
   * ENHANCED: Specialized handling for environment configuration issues
   * @param error Environment validation error
   * @returns Standardized error response
   */
  handleEnvironmentValidationError(error: EnvironmentValidationError): StandardErrorResponse {
    const logger = createLogger('GlobalErrorHandler');

    logger.error('❌ Environment validation error:', {
      message: error.message,
      validationResult: error.validationResult
    });

    // Create standardized error response for environment issues
    const standardError: StandardErrorResponse = {
      status: 0, // Use 0 to indicate environment/configuration error
      statusText: 'Environment Configuration Error',
      message: error.message,
      details: error.validationResult,
      timestamp: new Date().toISOString(),
      url: 'environment-validation',
      method: 'VALIDATION'
    };

    // Always show toast for environment errors as they are critical
    this.toastService.error('No server URL is available. Please check your environment configuration and retry again.');

    return standardError;
  }
}
