import { Injectable, inject, Ng<PERSON>one } from '@angular/core';
import { BehaviorSubject, Observable, Subscription, combineLatest } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ChangeDetectorRef } from '@angular/core';

import { UIDesignNode } from '../components/code-window/services/ui-design-node.service';
import { GenerateUIDesignService, UIDesignResponseData } from './generate-ui-design.service';
import { UIDesignSelectionService, MultiSelectedNodeData } from './ui-design-selection.service';
import { UIDesignEditService } from './ui-design-edit.service';
import { UIDesignNodePositioningService } from '../components/code-window/services/ui-design-node-positioning.service';
import { UIDesignVisualFeedbackService } from './ui-design-visual-feedback.service';
import { WireframeGenerationStateService } from './wireframe-generation-state.service';
import { UIDesignFilenameTransformerService } from './ui-design-filename-transformer.service';
import { WireframeNodeManagementService } from './wireframe-node-management.service';
import { UIDesignIntroService, IntroMessageState } from './ui-design-intro.service';
import { ToastService } from './toast.service';
import { createLogger } from '../utils/logger';

export interface UIDesignAPIResponse {
  pageName: string;
  content: string;
}

export interface WireframeAPIResponse {
  fileName: string;
  content: string;
}

export interface UIDesignPageData {
  fileName: string;
  content: string;
}

export interface MobilePage {
  fileName: string;
  content: string;
}

/**
 * UI Design Generation Orchestrator Service
 * 
 * This service handles all UI design generation functionality that was previously
 * embedded in the code-window component. It provides a clean separation of concerns
 * and follows Angular 19+ patterns with proper reactive state management.
 * 
 * Responsibilities:
 * - UI design mode initialization and cleanup
 * - Wireframe generation orchestration
 * - Node management and positioning
 * - Canvas interaction handling
 * - Selection and editing workflows
 * - State management for UI design operations
 */
@Injectable({
  providedIn: 'root'
})
export class UIDesignGenerationOrchestratorService {
  private readonly logger = createLogger('UIDesignGenerationOrchestratorService');
  
  // Angular 19+ dependency injection
  private readonly route = inject(ActivatedRoute);
  private readonly sanitizer = inject(DomSanitizer);
  private readonly ngZone = inject(NgZone);
  
  // Service dependencies
  private readonly generateUIDesignService = inject(GenerateUIDesignService);
  private readonly uiDesignSelectionService = inject(UIDesignSelectionService);
  private readonly uiDesignEditService = inject(UIDesignEditService);
  private readonly uiDesignNodePositioningService = inject(UIDesignNodePositioningService);
  private readonly uiDesignVisualFeedbackService = inject(UIDesignVisualFeedbackService);
  private readonly wireframeGenerationStateService = inject(WireframeGenerationStateService);
  private readonly uiDesignFilenameTransformerService = inject(UIDesignFilenameTransformerService);
  private readonly wireframeNodeManagementService = inject(WireframeNodeManagementService);
  private readonly uiDesignIntroService = inject(UIDesignIntroService);
  private readonly toastService = inject(ToastService);

  // UI Design Mode State
  private readonly isUIDesignMode$ = new BehaviorSubject<boolean>(false);
  private readonly uiDesignNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private readonly selectedUIDesignNode$ = new BehaviorSubject<UIDesignNode | null>(null);
  private readonly isUIDesignFullScreenOpen$ = new BehaviorSubject<boolean>(false);
  private readonly uiDesignViewMode$ = new BehaviorSubject<'mobile' | 'web'>('mobile');
  private readonly isUIDesignModalFullScreen$ = new BehaviorSubject<boolean>(false);
  private readonly isUIDesignCodeViewerOpen$ = new BehaviorSubject<boolean>(false);

  // Generation State
  private readonly isUIDesignGenerating$ = new BehaviorSubject<boolean>(false);
  private readonly uiDesignError$ = new BehaviorSubject<string | null>(null);
  private readonly uiDesignApiInProgress$ = new BehaviorSubject<boolean>(false);
  private readonly isWireframeGenerationComplete$ = new BehaviorSubject<boolean>(false);
  private readonly isUIDesignRegenerating$ = new BehaviorSubject<boolean>(false);
  private readonly uiDesignLoadingNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private readonly isUIDesignLoading$ = new BehaviorSubject<boolean>(false);

  // Canvas and Selection State
  private readonly showCanvasTooltip$ = new BehaviorSubject<boolean>(true);
  private readonly isEditingUIDesign$ = new BehaviorSubject<boolean>(false);

  // Overview State
  private readonly showUIDesignOverviewTab$ = new BehaviorSubject<boolean>(false);
  private readonly uiDesignPages$ = new BehaviorSubject<MobilePage[]>([]);
  private readonly currentUIDesignPageIndex$ = new BehaviorSubject<number>(0);

  // Intro Message State
  private readonly introMessageState$ = new BehaviorSubject<IntroMessageState>({
    isLoading: false,
    text: '',
    isTyping: false,
    shouldReplaceText: false,
    targetMessageId: null
  });

  // Public Observables - Angular 19+ reactive patterns
  readonly isUIDesignMode = this.isUIDesignMode$.asObservable();
  readonly uiDesignNodes = this.uiDesignNodes$.asObservable();
  readonly selectedUIDesignNode = this.selectedUIDesignNode$.asObservable();
  readonly isUIDesignFullScreenOpen = this.isUIDesignFullScreenOpen$.asObservable();
  readonly uiDesignViewMode = this.uiDesignViewMode$.asObservable();
  readonly isUIDesignModalFullScreen = this.isUIDesignModalFullScreen$.asObservable();
  readonly isUIDesignCodeViewerOpen = this.isUIDesignCodeViewerOpen$.asObservable();

  readonly isUIDesignGenerating = this.isUIDesignGenerating$.asObservable();
  readonly uiDesignError = this.uiDesignError$.asObservable();
  readonly uiDesignApiInProgress = this.uiDesignApiInProgress$.asObservable();
  readonly isWireframeGenerationComplete = this.isWireframeGenerationComplete$.asObservable();
  readonly isUIDesignRegenerating = this.isUIDesignRegenerating$.asObservable();
  readonly uiDesignLoadingNodes = this.uiDesignLoadingNodes$.asObservable();
  readonly isUIDesignLoading = this.isUIDesignLoading$.asObservable();

  readonly showCanvasTooltip = this.showCanvasTooltip$.asObservable();
  readonly isEditingUIDesign = this.isEditingUIDesign$.asObservable();

  readonly showUIDesignOverviewTab = this.showUIDesignOverviewTab$.asObservable();
  readonly uiDesignPages = this.uiDesignPages$.asObservable();
  readonly currentUIDesignPageIndex = this.currentUIDesignPageIndex$.asObservable();

  readonly introMessageState = this.introMessageState$.asObservable();

  // Private properties for internal state management
  private currentActiveMessageId: string | null = null;
  private subscription: Subscription | null = null;

  constructor() {
    this.initializeUIDesignMode();
    this.subscribeToUIDesignService();
    this.initializeUIDesignSelection();
  }

  /**
   * Initialize UI Design mode based on route data
   * Uses Angular 19+ takeUntilDestroyed for automatic cleanup
   */
  private initializeUIDesignMode(): void {
    this.route.data.pipe(takeUntilDestroyed()).subscribe(data => {
      const isUIDesignMode = data['cardType'] === 'Generate UI Design';
      const wasUIDesignMode = this.isUIDesignMode$.value;

      if (wasUIDesignMode && !isUIDesignMode) {
        this.cleanupUIDesignMode();
      }

      this.isUIDesignMode$.next(isUIDesignMode);

      if (isUIDesignMode) {
        this.setupUIDesignMode();
      }
    });
  }

  /**
   * Subscribe to UI Design service for response handling
   */
  private subscribeToUIDesignService(): void {
    this.generateUIDesignService.uiDesignResponse$
      .pipe(takeUntilDestroyed())
      .subscribe(responseData => {
        if (responseData) {
          this.handleUIDesignResponseForOverview(responseData);
        }
      });

    this.generateUIDesignService.showOverviewTab$
      .pipe(takeUntilDestroyed())
      .subscribe(show => {
        this.showUIDesignOverviewTab$.next(show);
      });
  }

  /**
   * Initialize UI Design selection handling
   */
  private initializeUIDesignSelection(): void {
    this.uiDesignSelectionService.reset();
    this.isEditingUIDesign$.next(false);

    this.uiDesignSelectionService.showCanvasTooltip
      .pipe(takeUntilDestroyed())
      .subscribe(showTooltip => {
        this.showCanvasTooltip$.next(showTooltip);
      });

    this.uiDesignSelectionService.isEditingInProgress
      .pipe(takeUntilDestroyed())
      .subscribe(isEditing => {
        this.isEditingUIDesign$.next(isEditing);
      });
  }

  /**
   * Setup UI Design mode - initialize all necessary state
   */
  setupUIDesignMode(): void {
    this.logger.info('🎨 Setting up UI Design mode');

    this.isUIDesignMode$.next(true);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignRegenerating$.next(false);

    this.clearAllLoadingNodes();
    this.wireframeGenerationStateService.reset();

    this.startUIDesignGeneration();
  }

  /**
   * Cleanup UI Design mode - reset all state
   */
  cleanupUIDesignMode(): void {
    this.logger.info('🧹 Cleaning up UI Design mode');

    this.isUIDesignMode$.next(false);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isUIDesignRegenerating$.next(false);

    this.clearAllLoadingNodes();
    this.uiDesignIntroService.resetIntroState();

    this.uiDesignNodes$.next([]);
    this.selectedUIDesignNode$.next(null);
    this.isUIDesignFullScreenOpen$.next(false);

    this.uiDesignSelectionService.reset();
    this.showCanvasTooltip$.next(true);
    this.isEditingUIDesign$.next(false);
  }

  /**
   * Check if UI Design workflow is currently active
   */
  isUIDesignWorkflowActive(): boolean {
    const hasUIDesignCanvas = !!document.querySelector('.ui-design-canvas');
    const hasUIDesignOverview = this.showUIDesignOverviewTab$.value;
    return hasUIDesignCanvas || hasUIDesignOverview;
  }

  /**
   * Check if UI Design data should be blocked for specific data types
   */
  shouldBlockUIDesignData(dataType: string): boolean {
    const isActualUIDesignWorkflow = this.isUIDesignMode$.value && this.isUIDesignWorkflowActive();

    if (!isActualUIDesignWorkflow) {
      return false;
    }

    const allowedDataTypes = ['logs', 'artifacts', 'status', 'progress'];
    return !allowedDataTypes.includes(dataType);
  }

  /**
   * Start UI Design generation process
   */
  startUIDesignGeneration(): void {
    this.logger.info('🚀 Starting UI Design generation');

    this.isUIDesignGenerating$.next(true);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);

    this.uiDesignNodes$.next([]);
    this.createLoadingNodes();

    const uiDesignData = this.generateUIDesignService.getUIDesignData();

    if (uiDesignData) {
      this.initiateParallelUIDesignAPICalls(uiDesignData);
    } else {
      this.showUIDesignError('No UI Design data found. Please go back and submit a prompt.');
    }
  }

  /**
   * Create loading nodes for initial generation
   */
  private createLoadingNodes(): void {
    const loadingNodes: UIDesignNode[] = [
      {
        id: 'ui-design-loading-node',
        type: 'ui-design',
        data: {
          title: 'Generating UI Design...',
          displayTitle: 'Generating UI Design...',
          htmlContent: this.sanitizer.bypassSecurityTrustHtml(`
            <div class="loading-placeholder">
              <div class="shimmer-effect"></div>
              <p>Creating your wireframe...</p>
            </div>
          `),
          rawContent: '',
          isLoading: true,
          width: 300,
          height: 400
        },
        position: { x: 100, y: 100 },
        selected: false,
        dragging: false,
        visible: true
      }
    ];

    this.uiDesignNodes$.next(loadingNodes);
  }

  /**
   * Initiate parallel UI Design API calls
   */
  private initiateParallelUIDesignAPICalls(uiDesignData: any): void {
    this.logger.info('📡 Initiating parallel UI Design API calls');

    this.uiDesignApiInProgress$.next(true);
    this.uiDesignError$.next(null);

    this.wireframeGenerationStateService.startGeneration();

    const apiRequest = this.generateUIDesignService.buildAPIRequest();
    const mainAPICall = this.generateUIDesignService.generateUIDesign(apiRequest);
    const userRequest = uiDesignData.prompt || '';

    this.uiDesignIntroService
      .executeParallelGeneration(userRequest, mainAPICall, this.currentActiveMessageId || undefined)
      .subscribe({
        next: result => {
          if (result.mainAPISuccess) {
            this.handleUIDesignSuccess(result.mainAPIResult);
            this.uiDesignIntroService.completeTextReplacement();
          } else {
            this.handleUIDesignFailure(new Error('Main API failed'));
          }
        },
        error: error => {
          this.handleUIDesignFailure(error);
        },
      });

    this.uiDesignIntroService.introMessageState$
      .pipe(takeUntilDestroyed())
      .subscribe((state: IntroMessageState) => {
        this.introMessageState$.next(state);
      });
  }

  /**
   * Handle successful UI Design response
   */
  private handleUIDesignSuccess(response: any): void {
    this.logger.info('✅ UI Design generation successful');

    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
    this.uiDesignError$.next(null);
    this.isUIDesignLoading$.next(false);

    try {
      if (this.isUIDesignResponse(response)) {
        this.processUIDesignResponse(response);
      } else {
        this.showUIDesignError('Invalid response format from wireframe generation API');
      }
    } catch (error) {
      this.showUIDesignError('Failed to process wireframe generation response');
    }
  }

  /**
   * Handle UI Design generation failure
   */
  private handleUIDesignFailure(error: any): void {
    this.logger.error('❌ UI Design generation failed', error);

    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignLoading$.next(false);

    const errorMessage = error?.message || error?.error?.message || 'Wireframe generation failed';
    this.uiDesignError$.next(errorMessage);

    this.wireframeGenerationStateService.setError(errorMessage);
    this.showUIDesignError(errorMessage);
  }

  /**
   * Show UI Design error with error node
   */
  private showUIDesignError(message: string): void {
    this.logger.error('🚨 Showing UI Design error:', message);

    this.clearAllLoadingNodes();
    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);

    this.toastService.error(message);

    const errorNode: UIDesignNode = {
      id: 'error-node',
      type: 'ui-design',
      data: {
        title: 'Error',
        displayTitle: 'Generation Error',
        htmlContent: this.sanitizer.bypassSecurityTrustHtml(`
          <div class="error-container">
            <div class="error-icon">⚠️</div>
            <h3>Generation Failed</h3>
            <p>${message}</p>
            <button onclick="window.location.reload()" class="retry-button">
              Try Again
            </button>
          </div>
        `),
        rawContent: '',
        isLoading: false,
        width: 300,
        height: 200
      },
      position: { x: 100, y: 100 },
      selected: false,
      dragging: false,
      visible: true
    };

    this.uiDesignNodes$.next([errorNode]);
  }

  /**
   * Clear all loading nodes
   */
  clearAllLoadingNodes(): void {
    this.uiDesignLoadingNodes$.next([]);

    const currentNodes = this.uiDesignNodes$.value;
    const nonLoadingNodes = currentNodes.filter(
      node =>
        !node.data.isLoading &&
        !node.id.startsWith('loading-') &&
        node.id !== 'ui-design-loading-node'
    );

    if (nonLoadingNodes.length !== currentNodes.length) {
      this.uiDesignNodes$.next(nonLoadingNodes);
    }
  }

  /**
   * Check if response is a valid UI Design or Wireframe response
   */
  private isUIDesignResponse(response: any): boolean {
    if (!response) return false;

    // Handle string response (JSON)
    if (typeof response === 'string') {
      try {
        const parsed = JSON.parse(response);
        return this.isValidUIDesignArray(parsed);
      } catch {
        return false;
      }
    }

    // Handle array response
    if (Array.isArray(response)) {
      return this.isValidUIDesignArray(response);
    }

    return false;
  }

  /**
   * Validate if array contains valid UI Design or Wireframe data
   */
  private isValidUIDesignArray(arr: any[]): boolean {
    if (!Array.isArray(arr) || arr.length === 0) return false;

    return arr.every(item => {
      return (
        typeof item === 'object' &&
        item !== null &&
        ((item.pageName && typeof item.pageName === 'string') ||
         (item.fileName && typeof item.fileName === 'string')) &&
        item.content &&
        typeof item.content === 'string'
      );
    });
  }

  /**
   * Process UI Design API response and create nodes
   */
  processUIDesignResponse(response: string | UIDesignAPIResponse[] | WireframeAPIResponse[]): void {
    this.logger.info('🔄 Processing UI Design response');

    this.clearAllLoadingNodes();

    try {
      let pages: (UIDesignAPIResponse | WireframeAPIResponse)[];

      if (typeof response === 'string') {
        try {
          pages = JSON.parse(response);
        } catch (parseError) {
          this.showUIDesignError('Failed to parse response data');
          return;
        }
      } else if (Array.isArray(response)) {
        pages = response;
      } else {
        this.showUIDesignError('Invalid response format');
        return;
      }

      if (!Array.isArray(pages) || pages.length === 0) {
        this.showUIDesignError('No pages found in response');
        return;
      }

      // Convert API response to UI Design page data
      const uiDesignPages: UIDesignPageData[] = pages.map((page, index) => {
        let pageName: string;
        let content: string;

        if ('fileName' in page) {
          // Wireframe format
          pageName = this.extractPageNameFromFileName(page.fileName);
          content = page.content;
        } else if ('pageName' in page) {
          // UI Design format
          pageName = page.pageName?.trim() || `Page ${index + 1}`;
          content = page.content;
        } else {
          pageName = `Page ${index + 1}`;
          content = (page as any).content || '';
        }

        return {
          fileName: pageName,
          content: content
        };
      });

      this.createUIDesignNodes(uiDesignPages);
    } catch (error) {
      this.showUIDesignError('Failed to process response data');
    }
  }

  /**
   * Extract clean page name from fileName
   */
  private extractPageNameFromFileName(fileName: string): string {
    if (!fileName || typeof fileName !== 'string') {
      return 'Untitled Page';
    }

    try {
      const transformResult = this.uiDesignFilenameTransformerService.transformFileName(fileName);
      return transformResult.displayTitle || 'Untitled Page';
    } catch (error) {
      this.logger.warn('Failed to transform filename:', fileName, error);
      return fileName.replace(/\.(html|htm)$/i, '').replace(/[_-]/g, ' ');
    }
  }

  /**
   * Create UI Design nodes from page data
   */
  private async createUIDesignNodes(pages: UIDesignPageData[]): Promise<void> {
    this.logger.info('🏗️ Creating UI Design nodes', { pageCount: pages.length });

    try {
      this.clearAllLoadingNodes();

      const currentNodes = this.uiDesignNodes$.value;

      // Convert pages to wireframe page data format
      const wireframePages = pages.map(page => ({
        fileName: page.fileName,
        content: page.content,
        pageName: page.fileName
      }));

      // Create position calculator
      const positionCalculator = (count: number, _existingNodes: any[]) => {
        const positioningResult = this.uiDesignNodePositioningService.calculateInitialGenerationPositions(count);
        return positioningResult.positions;
      };

      // Process the response using node management service
      const result = await this.wireframeNodeManagementService.processRegenerationResponse(
        wireframePages,
        currentNodes,
        positionCalculator
      );

      // Update nodes
      this.uiDesignNodes$.next(result.updatedNodes);

      // Enable UI Design mode
      this.isUIDesignMode$.next(true);

      // Calculate optimal viewport position
      if (result.updatedNodes.length > 0) {
        const nodePositions = result.updatedNodes.map(node => node.position);
        const optimalViewport = this.uiDesignNodePositioningService.calculateOptimalViewport(nodePositions);

        // Auto-center viewport (this would need to be handled by the component)
        this.logger.info('📍 Optimal viewport calculated', optimalViewport);
      }

      // Create overview data
      const mobilePages: MobilePage[] = pages.map(page => ({
        fileName: page.fileName,
        content: page.content
      }));

      const responseData: UIDesignResponseData = {
        pages: mobilePages,
        jobId: 'ui-design-' + Date.now(),
        projectId: 'ui-design-project-' + Date.now()
      };

      this.generateUIDesignService.setUIDesignResponse(responseData);

      // Set completion states
      this.uiDesignSelectionService.setNodesCreated(true);
      this.isWireframeGenerationComplete$.next(true);
      this.wireframeGenerationStateService.completeGeneration(result.updatedNodes.length);

      this.logger.info('✅ UI Design nodes created successfully', { nodeCount: result.updatedNodes.length });
    } catch (error) {
      this.showUIDesignError(
        'Failed to create design nodes: ' + (error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  // Additional methods will continue in the next part
}
