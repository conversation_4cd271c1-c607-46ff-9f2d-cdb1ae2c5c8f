import { Injectable, inject, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable, BehaviorSubject, Subject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { NewPollingResponseProcessorService } from './new-polling-response-processor.service';
import { PollingService } from './polling.service';
import { NewPollingResponse } from '../models/polling-response.interface';
import { SSEEvent } from './sse.service';
import { createLogger } from '../utils/logger';
import { SSEEventCacheService } from './sse-event-cache.service';
import { TemplateLoadingService } from './template-loading.service';
import { GenerationStateService } from './generation-state.service';


/**
* SSE Event Data interface matching the expected SSE response format
* Updated to match exact polling response structure for 100% compatibility
*
* CRITICAL: This interface must match the exact structure of polling responses
* to ensure 100% compatibility with existing components.
*/
export interface SSEEventData {
 status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
 progress: string;
 log: string;
 progress_description: string;
 metadata: any[];
 history?: any[]; // Optional history field for enhanced compatibility
 is_final?:boolean


 // Additional fields that might be present in SSE data
 details?: any; // For nested response format compatibility
 status_code?: number; // HTTP status code if present
}


/**
* Service to process SSE event data and transform it to match polling response format
*
* This service acts as a bridge between SSE events and the existing polling response
* processing infrastructure, ensuring 100% compatibility with existing components.
*
* Features:
* - Transforms SSE event data to polling response format
* - Maintains compatibility with NewPollingResponseProcessorService
* - Provides the same observables as polling service
* - Handles JSON parsing and error recovery
* - Implements Angular 19+ patterns with inject() and signals
* - ENHANCED: Event-based data segregation for first-cut generation vs regeneration
* - Supports 'initial-code-gen' and 'code-regen' event types for proper data isolation
*/
@Injectable({
 providedIn: 'root'
})
export class SSEDataProcessorService {
 private readonly logger = createLogger('SSEDataProcessorService');
 private readonly destroyRef = inject(DestroyRef);
 private readonly pollingProcessor = inject(NewPollingResponseProcessorService);
 private readonly pollingService = inject(PollingService);
 private readonly eventCacheService = inject(SSEEventCacheService);
 private readonly templateLoadingService = inject(TemplateLoadingService);
 private readonly generationStateService = inject(GenerationStateService);


 // Subjects that mirror the polling service interface
 private statusSubject = new BehaviorSubject<string>('idle');
 private progressSubject = new BehaviorSubject<string>('');
 private progressDescriptionSubject = new BehaviorSubject<string>('');
 private logsSubject = new BehaviorSubject<string[]>([]);
 private isProcessingSubject = new BehaviorSubject<boolean>(false);
 private errorSubject = new Subject<any>();

 private isFinalSubject = new BehaviorSubject<boolean>(false);

 // Additional subjects for preview handling
 private isPreviewEnabledSubject = new BehaviorSubject<boolean>(false);




 // Public observables matching polling service interface
 public readonly status$ = this.statusSubject.asObservable();
 public readonly progress$ = this.progressSubject.asObservable();
 public readonly progressDescription$ = this.progressDescriptionSubject.asObservable();
 public readonly logs$ = this.logsSubject.asObservable();
 public readonly isProcessing$ = this.isProcessingSubject.asObservable();
 public readonly error$ = this.errorSubject.asObservable();
 public readonly is_final$ = this.isFinalSubject.asObservable();

 // Additional observables for file and preview handling
 public readonly isPreviewEnabled$ = this.isPreviewEnabledSubject.asObservable();






 // Expose polling processor observables for direct access
 public readonly currentProgress$ = this.pollingProcessor.currentProgress$;
 public readonly currentStatus$ = this.pollingProcessor.currentStatus$;
 public readonly artifactData$ = this.pollingProcessor.artifactData$;
 public readonly fileList$ = this.pollingProcessor.fileList$;
 public readonly codeFiles$ = this.pollingProcessor.codeFiles$;
 public readonly previewUrl$ = this.pollingProcessor.previewUrl$;
 public readonly projectInfo$ = this.pollingProcessor.projectInfo$;


 // State tracking
 private accumulatedLogs: string[] = [];
 private lastProcessedEventId: string | null = null;


 // ENHANCED: Generation type tracking for data segregation
 private currentGenerationType: 'initial-code-gen' | 'code-regen' | 'unknown' = 'unknown';
 private processedEventsByType: Map<string, number> = new Map();

 // ENHANCED: Retry event filtering support
 private retryTimestamp: number | null = null;
 private isRetryFilteringEnabled = false;


 constructor() {
   this.logger.info('🔧 SSE Data Processor Service initialized with generation type segregation');
 }


 /**
  * Detect generation type from SSE event
  * ENHANCED: Implements event-based data segregation with improved logging
  */
 private detectGenerationType(sseEvent: SSEEvent): 'initial-code-gen' | 'code-regen' | 'unknown' {
   const eventType = sseEvent.event;


   this.logger.info('🔍 Detecting generation type from SSE event:', {
     eventType: eventType,
     eventId: sseEvent.id,
     hasData: !!sseEvent.data
   });


   if (eventType === 'initial-code-gen') {
     this.logger.info('🎯 Detected FIRST-CUT GENERATION event - stepper and logs will display this data');
     return 'initial-code-gen';
   } else if (eventType === 'code-regen') {
     this.logger.info('🔄 Detected REGENERATION event - stepper and logs will display this data');
     return 'code-regen';
   } else {
     // For backward compatibility, treat unknown/legacy events as unknown
     // This allows them to be processed without strict generation type filtering
     this.logger.info('❓ Unknown/legacy event type, will be processed for stepper/log display:', {
       eventType: eventType || 'undefined',
       willProcess: true,
       reason: 'Backward compatibility - all events processed for UI display'
     });
     return 'unknown';
   }
 }


 /**
  * Process SSE event and transform to polling response format
  * ENHANCED: Comprehensive processing to match polling exactly
  * @param sseEvent The SSE event to process
  * @returns Observable of processed response
  */
 processSSEEvent(sseEvent: SSEEvent, sessionKey?: string): Observable<any> {
   return new Observable(observer => {
     try {
       // ENHANCED: Check retry event filtering before processing
       if (!this.shouldProcessEventForRetry(sseEvent)) {
         this.logger.debug('🚫 Event filtered out by retry filter, skipping processing');
         observer.complete();
         return;
       }

       // Validate SSE event structure
       if (!this.validateSSEEvent(sseEvent)) {
         this.handleProcessingError(new Error('Invalid SSE event structure'), 'validation');
         observer.error(new Error('Invalid SSE event structure'));
         return;
       }

       // ENHANCED: Checkpoint-based event processing with intelligent caching
       if (sseEvent.id) {
         // Check if this event has already been processed
         if (this.eventCacheService.isEventProcessed(sseEvent.id, sessionKey)) {
           this.logger.info('⏭️ Skipping already processed SSE event (checkpoint):', {
             eventId: sseEvent.id,
             eventType: sseEvent.event,
             sessionKey,
             reason: 'Event already processed - preventing duplicate processing'
           });
           observer.complete();
           return;
         }

         // Cache the event for duplicate prevention
         const eventType = sseEvent.event as 'initial-code-gen' | 'code-regen' | 'update' | 'message';
         const wasCached = this.eventCacheService.cacheEvent(
           sseEvent.id,
           eventType,
           sessionKey
         );

         if (!wasCached) {
           this.logger.debug('📋 Event was already cached but not processed:', {
             eventId: sseEvent.id,
             eventType: sseEvent.event,
             sessionKey
           });
         }
       }

       // ENHANCED: Detect and track generation type for data segregation
       const generationType = this.detectGenerationType(sseEvent);
       this.currentGenerationType = generationType;

       this.logger.info('📨 Processing SSE event with checkpoint validation:', {
         type: sseEvent.event,
         id: sseEvent.id,
         sessionKey,
         generationType: generationType,
         hasEventId: !!sseEvent.id,
         checkpointEnabled: !!sseEvent.id,
         timestamp: new Date().toISOString()
       });

       // Check if event should be processed based on type and generation type
       if (!this.shouldProcessEvent(sseEvent, generationType)) {
         this.logger.info('⏭️ Skipping event due to filtering (this should be rare now):', {
           eventType: sseEvent.event,
           generationType: generationType,
           reason: 'Event filtered out by shouldProcessEvent logic'
         });
         observer.complete();
         return;
       }

       // Legacy duplicate check (now supplemented by checkpoint caching)
       if (this.isDuplicateEvent(sseEvent)) {
         this.logger.debug('⏭️ Skipping duplicate SSE event (legacy check):', sseEvent.id);
         observer.complete();
         return;
       }


       // Parse SSE event data
       const eventData = this.parseSSEEventData(sseEvent);

       this.isFinalSubject.next(!!eventData?.is_final);

       if (!eventData) {
         this.handleProcessingError(new Error('Failed to parse SSE event data'), 'parsing');
         observer.error(new Error('Invalid SSE event data'));
         return;
       }


       this.logger.info('🔍 Parsed SSE event data:', {
         status: eventData.status,
         progress: eventData.progress,
         hasLog: !!eventData.log,
         hasProgressDescription: !!eventData.progress_description,
         metadataCount: eventData.metadata.length,
         historyCount: eventData.history?.length || 0
       });


       // Transform to polling response format
       const pollingResponse = this.transformToPollingResponse(eventData);



       // Update internal state to maintain compatibility
       this.updateInternalState(eventData);

       // FEATURE: Seed Project Template Loading
       // Trigger template loading when SEED_PROJECT_INITIALIZED + IN_PROGRESS is detected
       this.handleSeedProjectTemplateLoading(eventData);

       // FEATURE: Repository Metadata Extraction
       // Extract repository metadata from SSE events for VS Code export
       this.handleRepositoryMetadataExtraction(eventData);

       // FEATURE: Code Tab Enabling for Seed Project
       // Enable code tab when SEED_PROJECT_INITIALIZED + IN_PROGRESS is detected
       this.handleCodeTabEnabling(eventData);

       // ENHANCED: Process code files for BUILD progress events BEFORE polling processor
       // This ensures the files are properly added to the polling response
       this.processCodeFilesForBuildProgress(eventData, pollingResponse, generationType);


       // CRITICAL: Process through existing polling processor for 100% compatibility
       // Only process if the generation type is appropriate for the current context
       this.processWithPollingProcessor(pollingResponse, generationType);


       // Update local subjects for backward compatibility (mirrors PollingService behavior)
       // CRITICAL: STEPPER ISOLATION - Do not update stepper subjects for code-regen events, but allow file processing
       if (generationType !== 'code-regen') {
         this.updateLocalSubjects(pollingResponse, eventData);
       } else {
         this.logger.info('🔄 Processing code-regen event for file updates while isolating stepper');
         // Allow file processing for code-regen events but skip stepper-specific updates
         this.updateLocalSubjectsForRegeneration(pollingResponse, eventData);
       }


       // ENHANCED: Mark event as processed in cache for checkpoint tracking
       if (sseEvent.id) {
         const wasMarked = this.eventCacheService.markEventProcessed(sseEvent.id, sessionKey);
         if (wasMarked) {
           this.logger.debug('✅ Event marked as processed in cache:', {
             eventId: sseEvent.id,
             sessionKey,
             eventType: sseEvent.event
           });
         }
       }

       // Emit the transformed response
       observer.next(pollingResponse);
       observer.complete();

       this.logger.info('✅ SSE event processed successfully with checkpoint tracking:', {
         eventId: sseEvent.id,
         sessionKey,
         finalStatus: pollingResponse.status,
         finalProgress: pollingResponse.progress,
         hasCodeFiles: this.hasCodeFilesInMetadata(eventData),
         checkpointEnabled: !!sseEvent.id
       });


     } catch (error) {
       this.handleProcessingError(error, 'main_processing');
       observer.error(error);
     }
   });
 }


 /**
  * Parse SSE event data from the event string
  * Enhanced to handle exact polling response format for 100% compatibility
  *
  * CRITICAL: This method must handle the same data formats as PollingService
  * to ensure identical processing results.
  */
 private parseSSEEventData(sseEvent: SSEEvent): SSEEventData | null {
   try {
     this.logger.debug('🔍 Parsing SSE event:', {
       type: sseEvent.event,
       id: sseEvent.id,
       dataType: typeof sseEvent.data
     });


     // Handle different data formats
     let eventData: any;


     if (typeof sseEvent.data === 'string') {
       // Try to parse as JSON - this is the main SSE data format
       eventData = JSON.parse(sseEvent.data);
       this.logger.debug('📋 Parsed SSE data as JSON:', eventData);
     } else {
       eventData = sseEvent.data;
       this.logger.debug('📋 Using SSE data directly:', eventData);
     }


     // Validate required fields
     if (!eventData || typeof eventData !== 'object') {
       this.logger.warn('❌ Invalid event data format:', eventData);
       return null;
     }


     // CRITICAL: Handle nested details structure like polling service
     // Check if this is the nested format with details field
     let actualData = eventData;
     if (eventData.details && typeof eventData.details === 'object') {
       this.logger.debug('📋 Detected nested details structure, extracting...');
       actualData = eventData.details;
     }


     // CRITICAL: Ensure exact compatibility with polling response format
     // The SSE data field should contain the same structure as polling responses
     const processedData: SSEEventData = {
       status: this.validateStatus(actualData.status),
       progress: actualData.progress || '',
       log: actualData.log || '',
       progress_description: actualData.progress_description || '',
       metadata: Array.isArray(actualData.metadata) ? actualData.metadata : [],
       history: Array.isArray(actualData.history) ? actualData.history : [],
       is_final : actualData.is_final || false,


       // Preserve additional fields for format detection
       details: eventData.details,
       status_code: eventData.status_code
     };


     this.logger.debug('✅ Successfully parsed SSE event data:', {
       status: processedData.status,
       progress: processedData.progress,
       metadataCount: processedData.metadata.length,
       historyCount: processedData.history?.length || 0,
       hasLog: !!processedData.log,
       hasProgressDescription: !!processedData.progress_description
     });


     return processedData;


   } catch (error) {
     this.logger.error('❌ Error parsing SSE event data:', error);
     return null;
   }
 }


 /**
  * Validate and normalize status field to match polling format
  */
 private validateStatus(status: any): 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' {
   if (typeof status === 'string') {
     const upperStatus = status.toUpperCase();
     if (['IN_PROGRESS', 'COMPLETED', 'FAILED'].includes(upperStatus)) {
       return upperStatus as 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
     }
   }


   // Default to IN_PROGRESS if status is invalid
   this.logger.warn('⚠️ Invalid status received, defaulting to IN_PROGRESS:', status);
   return 'IN_PROGRESS';
 }


 /**
  * Transform SSE event data to polling response format
  * CRITICAL: This must produce IDENTICAL structure to polling responses
  *
  * This method handles both flat and nested response formats to match
  * the exact behavior of PollingService response processing.
  */
 private transformToPollingResponse(eventData: SSEEventData): NewPollingResponse | any {
   // CRITICAL: If we have a nested details structure, create the same format
   // that PollingService expects for new polling response format
   if (eventData.details) {
     this.logger.debug('🔄 Creating nested polling response format');
     const nestedResponse = {
       status_code: eventData.status_code || 200,
       details: {
         status: eventData.status,
         progress: eventData.progress,
         log: eventData.log,
         progress_description: eventData.progress_description,
         history: eventData.history || [],
         metadata: eventData.metadata || []
       }
     };


     this.logger.debug('📋 Created nested response structure:', {
       hasStatusCode: !!nestedResponse.status_code,
       hasDetails: !!nestedResponse.details,
       detailsStatus: nestedResponse.details.status,
       detailsProgress: nestedResponse.details.progress
     });


     return nestedResponse;
   }


   // Create standard flat polling response structure
   const pollingResponse: NewPollingResponse = {
     status: eventData.status,
     progress: eventData.progress,
     log: eventData.log,
     progress_description: eventData.progress_description,
     history: eventData.history || [],
     metadata: eventData.metadata || []
   };


   this.logger.debug('🔄 Transformed SSE data to flat polling response format:', {
     status: pollingResponse.status,
     progress: pollingResponse.progress,
     metadataCount: pollingResponse.metadata.length,
     historyCount: pollingResponse.history.length,
     hasLog: !!pollingResponse.log,
     hasProgressDescription: !!pollingResponse.progress_description
   });


   // Validate the transformed response matches expected format
   this.validatePollingResponse(pollingResponse);


   return pollingResponse;
 }


 /**
  * Validate that the polling response has the expected structure
  */
 private validatePollingResponse(response: NewPollingResponse): void {
   const requiredFields = ['status', 'progress', 'log', 'progress_description', 'history', 'metadata'];
   const missingFields = requiredFields.filter(field => !(field in response));


   if (missingFields.length > 0) {
     this.logger.warn('⚠️ Polling response missing required fields:', missingFields);
   }


   if (!Array.isArray(response.metadata)) {
     this.logger.warn('⚠️ Metadata is not an array:', typeof response.metadata);
   }


   if (!Array.isArray(response.history)) {
     this.logger.warn('⚠️ History is not an array:', typeof response.history);
   }
 }


 /**
  * Update local subjects for backward compatibility
  * CRITICAL: This mirrors the exact behavior from PollingService.processNewPollingResponse()
  */
 private updateLocalSubjects(pollingResponse: any, eventData: SSEEventData): void {
   try {
     this.logger.debug('🔄 Updating local subjects for backward compatibility');


     // Extract the actual response data (handle nested details structure)
     let responseData = pollingResponse;
     if (pollingResponse.details) {
       responseData = pollingResponse.details;
     }


     // Update local subjects for backward compatibility - mirrors PollingService exactly
     this.progressSubject.next(responseData.progress);
     this.progressDescriptionSubject.next(responseData.progress_description);
     this.statusSubject.next(responseData.status);


     // Add log message with timestamp - exact format from PollingService
     if (responseData.log && responseData.log.trim() !== '') {
       const timestamp = this.getFormattedTimestamp();
       const logMessage = `${timestamp} - INFO - ${responseData.log}`;
       const currentLogs = this.logsSubject.getValue();
       this.logsSubject.next([...currentLogs, logMessage]);
       this.logger.debug('📋 Log message added with timestamp:', logMessage.substring(0, 100) + '...');
     }


     this.logger.debug('✅ Local subjects updated successfully');
   } catch (error) {
     this.logger.error('❌ Error updating local subjects:', error);
   }
 }


 /**
  * Update internal state based on SSE event data
  * ENHANCED: Mirror polling service state management exactly
  */
 private updateInternalState(eventData: SSEEventData): void {
   this.logger.debug('🔄 Updating internal state from SSE data');


   // Update status - use exact polling format mapping
   const pollingStatus = this.mapStatusToPollingFormat(eventData.status);
   this.statusSubject.next(pollingStatus);
   this.logger.debug('📊 Status updated:', { sse: eventData.status, polling: pollingStatus });


   // Update progress description - exact match to polling
   if (eventData.progress_description) {
     this.progressDescriptionSubject.next(eventData.progress_description);
     this.logger.debug('📝 Progress description updated:', eventData.progress_description.substring(0, 100) + '...');
   }


   // Update progress - use the exact progress value from SSE
   this.progressSubject.next(eventData.progress);
   this.logger.debug('📈 Progress updated:', eventData.progress);


   // Update processing state based on status
   const isProcessing = eventData.status === 'IN_PROGRESS';
   this.isProcessingSubject.next(isProcessing);
   this.logger.debug('⚙️ Processing state updated:', isProcessing);


   this.logger.debug('✅ Internal state update completed');
 }


 /**
  * Process the transformed response through the existing polling processor
  * CRITICAL: This ensures 100% compatibility with existing component expectations
  * ENHANCED: Includes generation type for proper data segregation
  *
  * This method mirrors the exact logic from PollingService.checkCodeGenerationStatus()
  * to ensure identical processing behavior.
  */
 private processWithPollingProcessor(
   pollingResponse: NewPollingResponse,
   generationType: 'initial-code-gen' | 'code-regen' | 'unknown'
 ): void {
   try {
     this.logger.debug('🔄 Processing through polling processor with generation type:', {
       processorType: 'NewPollingResponseProcessorService',
       responseStatus: pollingResponse.status,
       responseProgress: pollingResponse.progress,
       generationType: generationType
     });


     // CRITICAL: STEPPER ISOLATION - Do not process code-regen events for stepper
     // The stepper should only process initial-code-gen events to prevent interference
     if (generationType === 'code-regen') {
       this.logger.info('🔄 Processing code-regen event for file updates while isolating stepper');
       // Process files for code-regen but skip stepper updates
       this.processCodeRegenerationForFiles(pollingResponse);
       return; // Exit early to prevent stepper from processing regeneration events
     }


     // CRITICAL: Use the exact same format detection logic as PollingService
     // This ensures identical processing method selection


     // Check if this is the new polling response format (nested in details)
     const isNewPolling = this.isNewPollingResponseFormat(pollingResponse);


     if (isNewPolling) {
       this.logger.info('🔄 Detected new polling response format, processing with new processor and generation type:', generationType);
       this.pollingProcessor.processResponse(pollingResponse, generationType);
       return; // Exit early, new processor handles everything
     }


     // Check for enhanced polling response format
     if (this.isEnhancedPollingResponseFormat(pollingResponse)) {
       this.logger.debug('📡 Using enhanced response processor with generation type:', generationType);
       this.pollingProcessor.processEnhancedResponse(pollingResponse, generationType);
     } else if (this.hasNewWorkflowFormat(pollingResponse)) {
       this.logger.debug('🔄 Using new workflow processor with generation type:', generationType);
       this.pollingProcessor.processNewWorkflowResponse(pollingResponse as any, generationType);
     } else {
       this.logger.debug('📋 Using standard response processor with generation type:', generationType);
       this.pollingProcessor.processResponse(pollingResponse, generationType);
     }


     this.logger.debug('✅ Successfully processed through polling processor');


     // CRITICAL: Update polling service's lastStatusResponse for retry mechanism compatibility
     // This ensures that getErrorMessageFromLastResponse() can access SSE data
     this.updatePollingServiceState(pollingResponse);
   } catch (error) {
     this.logger.error('❌ Error in polling processor:', error);
     throw error;
   }
 }


 /**
  * Check if this is the new polling response format (nested in details)
  * Mirrors exact logic from PollingService.isNewPollingResponseFormat()
  */
 private isNewPollingResponseFormat(statusResponse: any): boolean {
   return statusResponse &&
          statusResponse.details &&
          typeof statusResponse.details === 'object' &&
          statusResponse.details.status &&
          statusResponse.details.progress &&
          statusResponse.details.log !== undefined &&
          statusResponse.details.progress_description !== undefined;
 }


 /**
  * Check if response matches enhanced polling format
  * Mirrors logic from PollingService.isEnhancedPollingResponseFormat()
  */
 private isEnhancedPollingResponseFormat(response: any): boolean {
   return response &&
          typeof response.status === 'string' &&
          typeof response.progress === 'string' &&
          typeof response.log === 'string' &&
          typeof response.progress_description === 'string' &&
          Array.isArray(response.metadata) &&
          !response.details; // Enhanced format doesn't have nested details
 }


 /**
  * Check if response has new workflow format with prev_metadata or enhanced history
  */
 private hasNewWorkflowFormat(response: any): boolean {
   return response && (
     Array.isArray(response.prev_metadata) ||
     (Array.isArray(response.history) && response.history.length > 0) ||
     (Array.isArray(response.metadata) && response.metadata.some((m: any) => m.type === 'artifact'))
   );
 }


 /**
  * Check if this is a duplicate event
  * ENHANCED: Better duplicate detection with event type consideration
  */
 private isDuplicateEvent(sseEvent: SSEEvent): boolean {
   if (!sseEvent.id) {
     return false; // No ID to compare
   }


   // Create composite key for better duplicate detection
   const eventKey = `${sseEvent.event || 'update'}-${sseEvent.id}`;


   if (this.lastProcessedEventId === eventKey) {
     this.logger.debug('🔄 Duplicate event detected:', eventKey);
     return true;
   }


   this.lastProcessedEventId = eventKey;
   return false;
 }


 /**
  * Check if SSE event should be processed based on event type and generation type filtering
  * ENHANCED: Fixed to allow stepper and logs to display data from all relevant event types
  * while maintaining data segregation for specific use cases
  */
 private shouldProcessEvent(sseEvent: SSEEvent, generationType: 'initial-code-gen' | 'code-regen' | 'unknown'): boolean {
   // Process generation-specific events and legacy events
   const eventType = sseEvent.event || 'update';


   // ENHANCED: Include generation-specific event types
   const processableTypes = ['update', 'status', 'progress', 'initial-code-gen', 'code-regen'];
   const skipTypes = ['ping', 'heartbeat', 'keep-alive'];


   if (skipTypes.includes(eventType)) {
     this.logger.debug('⏭️ Skipping event type:', eventType);
     return false;
   }


   // FIXED: Remove overly restrictive generation type validation
   // The previous logic was preventing stepper and logs from displaying data
   // from "initial-code-gen" and "code-regen" events. We now allow all
   // generation-specific events to be processed for stepper and log display
   // while maintaining data segregation through other mechanisms.


   // Log the event processing decision for debugging
   this.logger.debug('✅ Processing SSE event:', {
     eventType,
     detectedGenerationType: generationType,
     reason: 'Allowing all generation events for stepper/log display'
   });


   if (!processableTypes.includes(eventType)) {
     this.logger.warn('⚠️ Unknown event type, processing anyway:', eventType);
   }


   // Track processed events by type for analytics
   const currentCount = this.processedEventsByType.get(generationType) || 0;
   this.processedEventsByType.set(generationType, currentCount + 1);


   this.logger.debug('✅ Event approved for processing:', {
     eventType,
     generationType,
     processedCount: this.processedEventsByType.get(generationType)
   });


   // ENHANCED: Additional filtering for BUILD progress events
   try {
     const eventData = JSON.parse(sseEvent.data);


     // Log event data for debugging
     this.logger.debug('🔍 SSE Event filtering check:', {
       progress: eventData.progress,
       status: eventData.status,
       hasMetadata: !!eventData.metadata,
       metadataLength: eventData.metadata?.length || 0
     });


     // CRITICAL: Enhanced filtering for BUILD progress with code files
     if (eventData.progress === 'BUILD' &&
         (eventData.status === 'IN_PROGRESS' || eventData.status === 'COMPLETED')) {


       // Check if event contains files metadata
       const hasFilesMetadata = eventData.metadata &&
         Array.isArray(eventData.metadata) &&
         eventData.metadata.some((item: any) => item.type === 'files');


       if (hasFilesMetadata) {
         this.logger.info('✅ BUILD progress event with files metadata - processing for code display');
         return true;
       }
     }
   } catch (error) {
     this.logger.debug('⚠️ Could not parse SSE event data for filtering:', error);
   }


   return true;
 }


 /**
  * Process code files for BUILD progress events and CODE_GENERATION COMPLETED events
  * CRITICAL: Based on actual SSE data structure from console JSON
  * - Initial generation: BUILD progress events contain files
  * - Regeneration: CODE_GENERATION COMPLETED events contain files
  * - Event type determines processing: initial-code-gen vs code-regen
  */
 private processCodeFilesForBuildProgress(
   eventData: SSEEventData,
   pollingResponse: any,
   generationType: 'initial-code-gen' | 'code-regen' | 'unknown'
 ): void {
   // CRITICAL: Process based on actual SSE data patterns
   // Initial generation: BUILD progress with files
   const shouldProcessInitialBuild = generationType === 'initial-code-gen' &&
       eventData.progress === 'BUILD' &&
       (eventData.status === 'IN_PROGRESS' || eventData.status === 'COMPLETED');

   // Regeneration: CODE_GENERATION COMPLETED with files
   const shouldProcessRegeneration = generationType === 'code-regen' &&
       eventData.progress === 'CODE_GENERATION' &&
       eventData.status === 'COMPLETED';

   if (!shouldProcessInitialBuild && !shouldProcessRegeneration) {
     return;
   }


   this.logger.info('🔍 Processing SSE files based on console data structure:', {
     progress: eventData.progress,
     status: eventData.status,
     generationType: generationType,
     eventType: shouldProcessInitialBuild ? 'initial-build' : 'regeneration',
     metadataCount: eventData.metadata?.length || 0
   });


   // CRITICAL: Find files metadata exactly like console SSE structure
   // metadata: [{"type": "files", "data": [{"fileName": "src/index.css", "content": "..."}]}]
   const filesMetadata = eventData.metadata?.find((item: any) => item.type === 'files');

   if (!filesMetadata || !filesMetadata.data) {
     this.logger.debug('⚠️ No files metadata found in SSE event for', generationType);
     return;
   }


   try {
     // CRITICAL: Process files data exactly like console SSE structure
     // data: [{"fileName": "src/index.css", "content": "..."}]
     let files = filesMetadata.data;

     // Files should already be an array in SSE structure (no JSON parsing needed)
     if (!Array.isArray(files)) {
       // Fallback: try parsing if it's a string
       if (typeof files === 'string') {
         try {
           files = JSON.parse(files);
         } catch (parseError) {
           this.logger.error('❌ Failed to parse files JSON string:', parseError);
           return;
         }
       } else {
         this.logger.warn('⚠️ Files data is not an array:', typeof files);
         return;
       }
     }


     // CRITICAL: Convert to FileData format exactly like console SSE structure
     // Console structure: {"fileName": "src/index.css", "content": "..."}
     const fileData = files.map((file: any) => {
       const filePath = file.fileName || file.path || 'Unknown file';
       const fileCode = file.content || file.code || '';

       this.logger.debug(`📄 Processing SSE file: ${filePath} (${fileCode.length} chars)`);

       return {
         path: filePath,
         code: fileCode
       };
     });

     this.logger.info('✅ Successfully processed files from SSE event:', {
       fileCount: fileData.length,
       generationType: generationType,
       eventType: shouldProcessInitialBuild ? 'initial-build' : 'regeneration',
       files: fileData.map((f: any) => `${f.path} (${f.code.length} chars)`)
     });


     // CRITICAL: Ensure the polling response has the correct structure
     if (!pollingResponse.metadata) {
       pollingResponse.metadata = [];
     }


     // Add processed files to metadata in the exact same format as polling
     const processedFilesMetadata = {
       type: 'files',
       data: fileData  // Use the processed FileData format directly
     };


     // Replace or add files metadata
     const existingFilesIndex = pollingResponse.metadata.findIndex((item: any) => item.type === 'files');
     if (existingFilesIndex >= 0) {
       pollingResponse.metadata[existingFilesIndex] = processedFilesMetadata;
       this.logger.debug('🔄 Replaced existing files metadata at index:', existingFilesIndex);
     } else {
       pollingResponse.metadata.push(processedFilesMetadata);
       this.logger.debug('🆕 Added new files metadata to polling response');
     }


     // The processed files will be handled by the polling processor
     // when it processes the transformed polling response


   } catch (error) {
     this.logger.error('❌ Failed to process code files from SSE BUILD event:', error);
   }
 }


 /**
  * Check if event data contains code files in metadata
  * ENHANCED: More detailed logging for debugging
  */
 private hasCodeFilesInMetadata(eventData: SSEEventData): boolean {
   if (!eventData.metadata || !Array.isArray(eventData.metadata)) {
     return false;
   }


   const filesMetadata = eventData.metadata.find((item: any) => item.type === 'files');
   if (!filesMetadata || !filesMetadata.data) {
     return false;
   }


   // Log details about the files metadata for debugging
   let fileCount = 0;
   try {
     const files = typeof filesMetadata.data === 'string'
       ? JSON.parse(filesMetadata.data)
       : filesMetadata.data;
     if (Array.isArray(files)) {
       fileCount = files.length;
     }
   } catch (e) {
     this.logger.warn('⚠️ Failed to parse files metadata for count:', e);
   }


   this.logger.debug('📁 SSE event contains files metadata:', {
     progress: eventData.progress,
     status: eventData.status,
     fileCount
   });


   return true;
 }


 /**
  * Map SSE status to polling format
  */
 private mapStatusToPollingFormat(status: string): string {
   const statusMap: { [key: string]: string } = {
     'IN_PROGRESS': 'in-progress',
     'COMPLETED': 'completed',
     'FAILED': 'failed',
     'ERROR': 'error'
   };


   return statusMap[status] || 'in-progress';
 }


 /**
  * Generate progress message from event data
  * ENHANCED: Removed hardcoded messages - now uses dynamic SSE-driven content
  */
 private generateProgressMessage(eventData: SSEEventData): string {
   // AUDIT CLEANUP: Use progress_description from SSE data if available
   if (eventData.progress_description) {
     return eventData.progress_description;
   }


   // AUDIT CLEANUP: Use log field if available
   if (eventData.log) {
     return eventData.log;
   }


   // AUDIT CLEANUP: Minimal fallback - let the sequential service handle dynamic messages
   return `${eventData.progress || 'Processing'}...`;
 }


 /**
  * Add new log content to accumulated logs
  * ENHANCED: Match exact polling service log format and timestamp handling
  */
 private addToLogs(logContent: string): void {
   // Use the same timestamp format as polling service
   const timestamp = this.getFormattedTimestamp();
   const logEntry = `${timestamp} - INFO - ${logContent}`;


   // Prevent duplicate log entries
   if (!this.accumulatedLogs.includes(logEntry)) {
     this.accumulatedLogs.push(logEntry);
     this.logsSubject.next([...this.accumulatedLogs]);
     this.logger.debug('📝 Log entry added:', logEntry.substring(0, 100) + '...');
   } else {
     this.logger.debug('⏭️ Duplicate log entry skipped');
   }
 }


 /**
  * Get formatted timestamp matching polling service format
  * Mirrors the timestamp format used in PollingService
  */
 private getFormattedTimestamp(): string {
   const now = new Date();
   const hours = now.getHours().toString().padStart(2, '0');
   const minutes = now.getMinutes().toString().padStart(2, '0');
   const seconds = now.getSeconds().toString().padStart(2, '0');
   return `${hours}:${minutes}:${seconds}`;
 }


 /**
  * Get current generation type for external components
  * ENHANCED: Allows components to check current generation context
  */
 getCurrentGenerationType(): 'initial-code-gen' | 'code-regen' | 'unknown' {
   return this.currentGenerationType;
 }


 /**
  * Set generation type context for components that need to specify their context
  * ENHANCED: Allows components to set their processing context
  */
 setGenerationTypeContext(generationType: 'initial-code-gen' | 'code-regen' | 'unknown'): void {
   this.logger.info('🎯 Setting generation type context:', {
     previousType: this.currentGenerationType,
     newType: generationType
   });
   this.currentGenerationType = generationType;
 }


 /**
  * Get processing statistics by generation type
  * ENHANCED: Provides analytics for data segregation
  */
 getProcessingStatistics(): Map<string, number> {
   return new Map(this.processedEventsByType);
 }


 /**
  * Check if current context is first-cut generation
  * ENHANCED: Helper method for UI components
  */
 isFirstCutGeneration(): boolean {
   return this.currentGenerationType === 'initial-code-gen';
 }


 /**
  * Check if current context is regeneration
  * ENHANCED: Helper method for UI components
  */
 isRegeneration(): boolean {
   return this.currentGenerationType === 'code-regen';
 }


 /**
  * Update local subjects for code-regen events (file processing only)
  * ENHANCED: Allows file updates for regeneration while isolating stepper
  */
 private updateLocalSubjectsForRegeneration(pollingResponse: NewPollingResponse, eventData: SSEEventData): void {
   this.logger.info('🔄 Updating local subjects for code-regen (files only):', {
     hasFiles: !!pollingResponse.metadata?.find(m => m.type === 'files'),
     progress: eventData.progress,
     status: eventData.status
   });

   // Only update file-related subjects for code-regen events through polling processor
   if (pollingResponse.metadata) {
     const filesMetadata = pollingResponse.metadata.find(m => m.type === 'files');
     if (filesMetadata && filesMetadata.data) {
       // Process files through polling processor to maintain consistency
       this.pollingProcessor.processResponse(pollingResponse, 'code-regen');
       this.logger.info('📁 Updated code files from code-regen event via polling processor:', filesMetadata.data.length);
     }

     // Update preview URL if available
     const urlMetadata = pollingResponse.metadata.find(m => m.type === 'ref_code');
     if (urlMetadata && urlMetadata.data) {
       // Process preview URL through polling processor
       this.pollingProcessor.processResponse(pollingResponse, 'code-regen');
       this.isPreviewEnabledSubject.next(true);
       this.logger.info('🔗 Updated preview URL from code-regen event via polling processor:', urlMetadata.data);
     }
   }
 }


 /**
  * Process code regeneration for file updates only
  * ENHANCED: Handles code-regen events for file processing while isolating stepper
  */
 private processCodeRegenerationForFiles(pollingResponse: NewPollingResponse): void {
   this.logger.info('🔄 Processing code-regen for file updates only');

   // Process files if available through polling processor
   if (pollingResponse.metadata) {
     const filesMetadata = pollingResponse.metadata.find(m => m.type === 'files');
     if (filesMetadata && filesMetadata.data) {
       // Process files through polling processor to maintain consistency
       this.pollingProcessor.processResponse(pollingResponse, 'code-regen');
       this.logger.info('📁 Processed files from code-regen event via polling processor:', filesMetadata.data.length);
     }
   }
 }


 /**
  * Reset processor state
  * ENHANCED: Complete state reset with polling processor cleanup and generation type reset
  */
 reset(): void {
   this.logger.info('🔄 Resetting SSE data processor state with generation type tracking');


   // Reset internal subjects
   this.statusSubject.next('idle');
   this.progressSubject.next('');
   this.progressDescriptionSubject.next('');
   this.accumulatedLogs = [];
   this.logsSubject.next([]);
   this.isProcessingSubject.next(false);
   this.lastProcessedEventId = null;

   // Reset preview subject
   this.isPreviewEnabledSubject.next(false);


   // ENHANCED: Reset generation type tracking
   this.currentGenerationType = 'unknown';
   this.processedEventsByType.clear();


   // Note: We don't reset the polling processor as it may be shared
   // and other components might be using it


   this.logger.info('✅ SSE data processor state reset completed with generation type tracking');
 }


 /**
  * Handle processing errors with recovery mechanisms
  */
 private handleProcessingError(error: any, context: string): void {
   this.logger.error(`❌ SSE processing error in ${context}:`, error);


   // Emit error for monitoring
   this.errorSubject.next({
     context,
     error,
     timestamp: new Date().toISOString()
   });


   // Update status to indicate error state
   this.statusSubject.next('error');
   this.isProcessingSubject.next(false);
 }


 /**
  * Validate SSE event structure before processing
  */
 private validateSSEEvent(sseEvent: SSEEvent): boolean {
   if (!sseEvent) {
     this.logger.warn('⚠️ Null or undefined SSE event');
     return false;
   }


   if (!sseEvent.data) {
     this.logger.warn('⚠️ SSE event missing data field');
     return false;
   }


   return true;
 }


 /**
  * ENHANCED: Extract error message from SSE event data
  * Handles different error message formats and provides fallback
  * CRITICAL: Prioritizes data.log field as specified in requirements
  */
 private extractErrorMessageFromSSEData(eventData: SSEEventData): string {
   // CRITICAL: First try to extract from data.log field as specified in requirements
   if (eventData.log && typeof eventData.log === 'string') {
     try {
       // Try to parse JSON log
       const logData = JSON.parse(eventData.log);
       if (logData.message) {
         this.logger.info('📋 Extracted error message from parsed log data:', {
           messageLength: logData.message.length,
           messagePreview: logData.message.substring(0, 100) + '...'
         });
         return logData.message;
       }
       this.logger.info('📋 Using raw log data as error message:', {
         logLength: eventData.log.length,
         logPreview: eventData.log.substring(0, 100) + '...'
       });
       return eventData.log;
     } catch {
       // Return raw log if not JSON
       this.logger.info('📋 Using raw log data as error message (not JSON):', {
         logLength: eventData.log.length,
         logPreview: eventData.log.substring(0, 100) + '...'
       });
       return eventData.log;
     }
   }

   // Try to extract from progress_description field as fallback
   if (eventData.progress_description && typeof eventData.progress_description === 'string') {
     this.logger.info('📋 Using progress_description field as fallback:', {
       messageLength: eventData.progress_description.length,
       messagePreview: eventData.progress_description.substring(0, 100) + '...'
     });
     return eventData.progress_description.trim();
   }

   // Final fallback
   this.logger.warn('⚠️ No error message found in SSE event data, using generic message');
   return 'An error occurred during processing. Please try again.';
 }

 /**
  * Update polling service's internal state with SSE data
  * CRITICAL: This ensures retry mechanism can access error messages from SSE data
  * ENHANCEMENT: Maintains 100% backward compatibility with polling-based retry
  * ENHANCED: Now includes proper error message extraction from log field
  */
 private updatePollingServiceState(pollingResponse: NewPollingResponse): void {
   try {
     this.logger.debug('🔄 Updating polling service state with SSE data for retry compatibility');

     // ENHANCED: Extract proper error message from log field for FAILED events
     let enhancedProgressDescription = pollingResponse.progress_description;
     if (pollingResponse.status === 'FAILED') {
       const extractedErrorMessage = this.extractErrorMessageFromSSEData({
         status: pollingResponse.status as 'FAILED',
         progress: pollingResponse.progress,
         log: pollingResponse.log,
         progress_description: pollingResponse.progress_description,
         metadata: pollingResponse.metadata,
         history: pollingResponse.history
       });

       // Use extracted error message as progress description for error display
       enhancedProgressDescription = extractedErrorMessage;
       this.logger.info('🔧 Enhanced progress description with extracted error message for FAILED event:', {
         originalLength: pollingResponse.progress_description?.length || 0,
         enhancedLength: enhancedProgressDescription.length,
         preview: enhancedProgressDescription.substring(0, 100) + '...'
       });
     }

     // Create a response format that matches what polling service expects
     // This ensures getErrorMessageFromLastResponse() can extract error messages
     const pollingServiceResponse = {
       status_code: 200,
       details: {
         status: pollingResponse.status,
         progress: pollingResponse.progress,
         log: pollingResponse.log,
         progress_description: enhancedProgressDescription, // Use enhanced error message
         metadata: pollingResponse.metadata,
         history: pollingResponse.history
       }
     };

     // Use reflection to update the private lastStatusResponse field
     // This is necessary for backward compatibility with existing retry mechanism
     (this.pollingService as any).lastStatusResponse = pollingServiceResponse;

     this.logger.debug('✅ Polling service state updated successfully for retry compatibility', {
       hasLog: !!pollingResponse.log,
       status: pollingResponse.status,
       progress: pollingResponse.progress,
       logLength: pollingResponse.log?.length || 0,
       enhancedErrorMessage: pollingResponse.status === 'FAILED'
     });

   } catch (error) {
     this.logger.error('❌ Failed to update polling service state:', error);
     // Don't throw - this is a compatibility enhancement, not critical for SSE functionality
   }
 }


 /**
  * Get current processor state
  * ENHANCED: Comprehensive state information for debugging
  */
 getCurrentState() {
   return {
     // Internal state
     status: this.statusSubject.value,
     progress: this.progressSubject.value,
     progressDescription: this.progressDescriptionSubject.value,
     logsCount: this.accumulatedLogs.length,
     isProcessing: this.isProcessingSubject.value,
     lastEventId: this.lastProcessedEventId,


     // Polling processor state (for comparison)
     pollingProcessorState: {
       currentProgress: this.pollingProcessor.currentProgress$,
       currentStatus: this.pollingProcessor.currentStatus$,
       progressDescription: this.pollingProcessor.progressDescription$
     },


     // Debugging info
     timestamp: new Date().toISOString(),
     processorVersion: '2.0.0-sse-enhanced'
   };
 }


 /**
  * Compare SSE processed data with expected polling format
  * Useful for debugging and verification
  */
 compareWithPollingFormat(sseData: any, expectedPollingData: any): any {
   const comparison = {
     matches: {
       status: sseData.status === expectedPollingData.status,
       progress: sseData.progress === expectedPollingData.progress,
       log: sseData.log === expectedPollingData.log,
       progress_description: sseData.progress_description === expectedPollingData.progress_description,
       metadata: JSON.stringify(sseData.metadata) === JSON.stringify(expectedPollingData.metadata)
     },
     differences: {},
     compatibility: 'unknown'
   };


   // Calculate differences
   Object.keys(comparison.matches).forEach(key => {
     if (!comparison.matches[key as keyof typeof comparison.matches]) {
       (comparison.differences as any)[key] = {
         sse: (sseData as any)[key],
         expected: (expectedPollingData as any)[key]
       };
     }
   });


   // Determine compatibility level
   const matchCount = Object.values(comparison.matches).filter(Boolean).length;
   const totalFields = Object.keys(comparison.matches).length;


   if (matchCount === totalFields) {
     comparison.compatibility = 'perfect';
   } else if (matchCount >= totalFields * 0.8) {
     comparison.compatibility = 'good';
   } else if (matchCount >= totalFields * 0.6) {
     comparison.compatibility = 'partial';
   } else {
     comparison.compatibility = 'poor';
   }


   this.logger.debug('🔍 SSE vs Polling comparison:', comparison);
   return comparison;
 }

 /**
  * ENHANCED: Enable retry event filtering for retry operations
  * Filters SSE events to process only those that occur after retry trigger
  * @param retryTimestamp Timestamp when retry was triggered
  */
 enableRetryEventFiltering(retryTimestamp: number): void {
   this.logger.info('🔄 Enabling retry event filtering in data processor:', {
     retryTimestamp,
     currentTime: Date.now(),
     filteringEnabled: true
   });

   this.retryTimestamp = retryTimestamp;
   this.isRetryFilteringEnabled = true;

   this.logger.info('✅ Retry event filtering enabled in data processor');
 }

 /**
  * ENHANCED: Disable retry event filtering
  * Removes event filtering to process all SSE events normally
  */
 disableRetryEventFiltering(): void {
   this.logger.info('🔄 Disabling retry event filtering in data processor');

   this.retryTimestamp = null;
   this.isRetryFilteringEnabled = false;

   this.logger.info('✅ Retry event filtering disabled in data processor');
 }

 /**
  * ENHANCED: Check if SSE event should be processed based on retry filtering
  * @param sseEvent The SSE event to check
  * @returns Whether the event should be processed
  */
 private shouldProcessEventForRetry(sseEvent: SSEEvent): boolean {
   if (!this.isRetryFilteringEnabled || !this.retryTimestamp) {
     return true; // No filtering enabled, process all events
   }

   // ENHANCED: Always allow progress description updates during retry operations
   // This ensures stepper progress text updates are not blocked during retry
   try {
     const eventData = JSON.parse(sseEvent.data);
     if (eventData.progress_description && eventData.status === 'IN_PROGRESS') {
       this.logger.debug('✅ Allowing progress description update during retry:', {
         eventId: sseEvent.id,
         progress: eventData.progress,
         status: eventData.status,
         hasProgressDescription: !!eventData.progress_description,
         reason: 'Progress description updates always allowed during retry'
       });
       return true;
     }
   } catch (error) {
     // If we can't parse the event data, fall back to timestamp filtering
     this.logger.debug('⚠️ Could not parse event data for retry filtering, using timestamp filter');
   }

   // Extract timestamp from event ID or use current time as fallback
   const eventTimestamp = this.extractEventTimestamp(sseEvent);

   // ENHANCED: If we can't extract timestamp, allow the event (be more lenient)
   if (!eventTimestamp) {
     this.logger.debug('✅ Event passes retry filter (no timestamp available):', {
       eventId: sseEvent.id,
       reason: 'No timestamp available, allowing event to prevent blocking legitimate updates'
     });
     return true;
   }

   if (eventTimestamp >= this.retryTimestamp) {
     this.logger.debug('✅ Event passes retry filter:', {
       eventId: sseEvent.id,
       eventTimestamp,
       retryTimestamp: this.retryTimestamp,
       shouldProcess: true
     });
     return true;
   }

   this.logger.debug('🚫 Event filtered out by retry filter:', {
     eventId: sseEvent.id,
     eventTimestamp,
     retryTimestamp: this.retryTimestamp,
     shouldProcess: false
   });
   return false;
 }

 /**
  * ENHANCED: Extract timestamp from SSE event ID
  * @param sseEvent The SSE event
  * @returns Extracted timestamp or null if not available
  */
 private extractEventTimestamp(sseEvent: SSEEvent): number | null {
   if (!sseEvent.id) {
     return null;
   }

   // ENHANCED: Try multiple timestamp extraction patterns
   // Pattern 1: timestamp-sequence (e.g., "1234567890-1")
   let match = sseEvent.id.match(/^(\d+)-/);
   if (match) {
     const timestamp = parseInt(match[1], 10);
     if (!isNaN(timestamp) && timestamp > 0) {
       return timestamp;
     }
   }

   // Pattern 2: Pure timestamp (e.g., "1234567890")
   if (/^\d+$/.test(sseEvent.id)) {
     const timestamp = parseInt(sseEvent.id, 10);
     if (!isNaN(timestamp) && timestamp > 0) {
       return timestamp;
     }
   }

   // Pattern 3: UUID or other formats - try to extract any timestamp-like number
   match = sseEvent.id.match(/(\d{10,13})/); // Look for 10-13 digit numbers (Unix timestamps)
   if (match) {
     const timestamp = parseInt(match[1], 10);
     if (!isNaN(timestamp) && timestamp > 0) {
       return timestamp;
     }
   }

   this.logger.debug('⚠️ Could not extract timestamp from event ID:', {
     eventId: sseEvent.id,
     reason: 'No recognizable timestamp pattern found'
   });
   return null;
 }

 /**
  * FEATURE: Handle Seed Project Template Loading
  * Triggers template loading when SEED_PROJECT_INITIALIZED + IN_PROGRESS is detected
  * @param eventData The SSE event data to check
  */
 private handleSeedProjectTemplateLoading(eventData: SSEEventData): void {
   try {
     // DEBUG: Log ALL SSE events to see what we're receiving
     console.log('🔍 DEBUG: SSE Event received in handleSeedProjectTemplateLoading', {
       progress: eventData.progress,
       status: eventData.status,
       timestamp: new Date().toISOString()
     });

     // DEBUG: Log all SEED_PROJECT_INITIALIZED events
     if (eventData.progress === 'SEED_PROJECT_INITIALIZED') {
       console.log('🔍 DEBUG: SEED_PROJECT_INITIALIZED event detected', {
         progress: eventData.progress,
         status: eventData.status,
         shouldTrigger: eventData.status === 'IN_PROGRESS'
       });
     }

     // ONLY trigger template loading for SEED_PROJECT_INITIALIZED + IN_PROGRESS
     // This ensures we only load seed project template files, not design system files
     if (eventData.progress === 'SEED_PROJECT_INITIALIZED' && eventData.status === 'IN_PROGRESS') {
       console.log('🏗️ Seed project initialized - loading template');

       // Get framework and design library from generation state service
       const framework = this.generationStateService.getCurrentFramework();
       const designLibrary = this.generationStateService.getCurrentDesignLibrary();

       if (framework && designLibrary) {
         // Update template loading state
         this.generationStateService.updateTemplateLoadingState(true);

         // Trigger template loading ONLY for seed project
         this.templateLoadingService.loadTemplate(framework, designLibrary).subscribe({
           next: (templateFiles) => {
             this.logger.info('✅ SSE-triggered template loading completed successfully', {
               fileCount: templateFiles.length,
               framework,
               designLibrary,
               files: templateFiles.map(f => f.name),
               trigger: 'SEED_PROJECT_INITIALIZED + IN_PROGRESS'
             });
             this.generationStateService.updateTemplateLoadingState(false);

             // Log extracted files for debugging
             this.logger.debug('📄 Template files extracted from SSE trigger:', {
               totalFiles: templateFiles.length,
               fileNames: templateFiles.map(f => f.name),
               fileSizes: templateFiles.map(f => ({ name: f.name, size: f.content?.length || 0 })),
               averageFileSize: templateFiles.length > 0 ?
                 Math.round(templateFiles.reduce((sum, f) => sum + (f.content?.length || 0), 0) / templateFiles.length) : 0,
               largestFile: templateFiles.reduce((largest, f) =>
                 (f.content?.length || 0) > (largest.content?.length || 0) ? f : largest, templateFiles[0] || {})?.name
             });

             // Verify Monaco Editor integration readiness
             this.logger.debug('🔍 Monaco Editor integration check:', {
               hasTemplateFiles: templateFiles.length > 0,
               allFilesHaveContent: templateFiles.every(f => f.content !== undefined),
               allFilesHaveNames: templateFiles.every(f => f.name),
               codeTabEnabled: this.generationStateService.isCodeTabEnabled()
             });
           },
           error: (error) => {
             this.logger.error('❌ SSE-triggered template loading failed', {
               error: error.message || error,
               framework,
               designLibrary,
               trigger: 'SEED_PROJECT_INITIALIZED + IN_PROGRESS',
               stack: error.stack
             });
             this.generationStateService.updateTemplateLoadingState(false);
             // Error handling is already done in the template service
           }
         });
       } else {
         this.logger.warn('⚠️ Cannot load template - missing framework or design library selections', {
           framework,
           designLibrary
         });
       }
     }
   } catch (error) {
     this.logger.error('❌ Error in handleSeedProjectTemplateLoading', error);
   }
 }

 /**
  * FEATURE: Handle Repository Metadata Extraction
  * Extracts repository metadata from SSE events for VS Code export
  * @param eventData The SSE event data to check for repository metadata
  */
 private handleRepositoryMetadataExtraction(eventData: SSEEventData): void {
   try {
     // Check if metadata contains repository information
     if (eventData.metadata && Array.isArray(eventData.metadata)) {
       for (const metadataItem of eventData.metadata) {
         if (metadataItem.type === 'artifact' && metadataItem.data) {
           try {
             // Parse the metadata data (it might be JSON string or object)
             const artifactData = typeof metadataItem.data === 'string'
               ? JSON.parse(metadataItem.data)
               : metadataItem.data;

             // Look for repository information
             if (artifactData.clone_url || artifactData.repository_url) {
               const repositoryMetadata = {
                 cloneUrl: artifactData.clone_url || artifactData.repository_url,
                 repositoryName: artifactData.repository_name || artifactData.name || 'Generated Project',
                 repositoryUrl: artifactData.repository_url || artifactData.clone_url,
                 branchName: artifactData.branch_name || artifactData.branch,
                 commitHash: artifactData.commit_hash || artifactData.commit
               };

               this.logger.info('🔗 Extracted repository metadata from SSE event', repositoryMetadata);
               this.generationStateService.updateRepositoryMetadata(repositoryMetadata);
               break; // Found repository metadata, no need to continue
             }
           } catch (parseError) {
             this.logger.warn('⚠️ Failed to parse artifact metadata', {
               error: parseError,
               metadataItem
             });
           }
         }
       }
     }
   } catch (error) {
     this.logger.error('❌ Error in handleRepositoryMetadataExtraction', error);
   }
 }

 /**
  * FEATURE: Handle Code Tab Enabling
  * Enables code tab when SEED_PROJECT_INITIALIZED + IN_PROGRESS is detected
  * @param eventData The SSE event data to check
  */
 private handleCodeTabEnabling(eventData: SSEEventData): void {
   try {
     // Check if this is the trigger condition: SEED_PROJECT_INITIALIZED + IN_PROGRESS
     if (eventData.progress === 'SEED_PROJECT_INITIALIZED' && eventData.status === 'IN_PROGRESS') {
       this.logger.info('📝 Detected SEED_PROJECT_INITIALIZED + IN_PROGRESS - enabling code tab');
       this.generationStateService.updateCodeTabState(true);
     }
   } catch (error) {
     this.logger.error('❌ Error in handleCodeTabEnabling', error);
   }
 }
}



