import { Injectable, inject, signal, DestroyRef, <PERSON>Zone } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

// Services
import { GenerateUIDesignService, UIDesignResponseData } from './generate-ui-design.service';
import { UIDesignSelectionService, MultiSelectedNodeData } from './ui-design-selection.service';
import { UIDesignEditService } from './ui-design-edit.service';
import { UIDesignCanvasService } from './ui-design-canvas.service';
import { UIDesignViewportService } from './ui-design-viewport.service';
import { UIDesignIntroService, IntroMessageState } from './ui-design-intro.service';
import { UIDesignFilenameTransformerService } from './ui-design-filename-transformer.service';
import { WireframeGenerationStateService } from './wireframe-generation-state.service';
import { WireframeNodeManagementService } from './wireframe-node-management.service';
import { UIDesignVisualFeedbackService } from './ui-design-visual-feedback.service';
import { ToastService } from './toast.service';

// Models
import { UIDesignNode } from '../models/ui-design-node.model';
import { UIDesignAPIResponse, WireframeAPIResponse } from '../models/ui-design-api.model';
import { MobilePage } from '../components/mobile-frame/mobile-frame.component';

/**
 * Comprehensive UI Design Generation Service
 *
 * This service handles ALL UI design generation functionality extracted from code-window component:
 * - Wireframe generation and management
 * - UI design state management
 * - Node creation and manipulation
 * - Canvas interactions and viewport management
 * - Modal management and full-screen operations
 * - Chat message integration and text replacement
 * - Loading states and error handling
 * - API orchestration and response processing
 *
 * Follows Angular 19+ patterns with inject(), Signals, and takeUntilDestroyed()
 */
@Injectable({
  providedIn: 'root'
})
export class UIDesignGenerationService {
  // Dependency injection using inject() pattern
  private readonly destroyRef = inject(DestroyRef);
  private readonly sanitizer = inject(DomSanitizer);
  private readonly ngZone = inject(NgZone);

  // Core services
  private readonly generateUIDesignService = inject(GenerateUIDesignService);
  private readonly uiDesignSelectionService = inject(UIDesignSelectionService);
  private readonly uiDesignEditService = inject(UIDesignEditService);
  private readonly uiDesignCanvasService = inject(UIDesignCanvasService);
  private readonly uiDesignViewportService = inject(UIDesignViewportService);
  private readonly uiDesignIntroService = inject(UIDesignIntroService);
  private readonly uiDesignFilenameTransformerService = inject(UIDesignFilenameTransformerService);
  private readonly wireframeGenerationStateService = inject(WireframeGenerationStateService);
  private readonly wireframeNodeManagementService = inject(WireframeNodeManagementService);
  private readonly uiDesignVisualFeedbackService = inject(UIDesignVisualFeedbackService);
  private readonly toastService = inject(ToastService);

  // ===== UI Design State Management =====

  // Core UI Design state
  private readonly isUIDesignMode$ = new BehaviorSubject<boolean>(false);
  private readonly uiDesignNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private readonly selectedUIDesignNode$ = new BehaviorSubject<UIDesignNode | null>(null);
  private readonly isUIDesignFullScreenOpen$ = new BehaviorSubject<boolean>(false);
  private readonly uiDesignViewMode$ = new BehaviorSubject<'mobile' | 'web'>('mobile');
  private readonly isUIDesignModalFullScreen$ = new BehaviorSubject<boolean>(false);
  private readonly isUIDesignCodeViewerOpen$ = new BehaviorSubject<boolean>(false);

  // Generation state
  private readonly isUIDesignGenerating$ = new BehaviorSubject<boolean>(false);
  private readonly uiDesignError$ = new BehaviorSubject<string | null>(null);
  private readonly uiDesignApiInProgress$ = new BehaviorSubject<boolean>(false);
  private readonly isWireframeGenerationComplete$ = new BehaviorSubject<boolean>(false);
  private readonly isUIDesignRegenerating$ = new BehaviorSubject<boolean>(false);
  private readonly uiDesignLoadingNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private readonly isUIDesignLoading$ = new BehaviorSubject<boolean>(false);

  // Canvas and interaction state
  private readonly showCanvasTooltip$ = new BehaviorSubject<boolean>(true);
  private readonly isEditingUIDesign$ = new BehaviorSubject<boolean>(false);
  private readonly introMessageState$ = new BehaviorSubject<IntroMessageState>({
    shouldReplaceText: false,
    targetMessageId: '',
    text: ''
  });

  // Overview tab state
  private readonly showUIDesignOverviewTab$ = new BehaviorSubject<boolean>(false);

  // ===== Angular 19+ Signals =====

  readonly isUIDesignModeSignal = signal<boolean>(false);
  readonly uiDesignNodesSignal = signal<UIDesignNode[]>([]);
  readonly selectedUIDesignNodeSignal = signal<UIDesignNode | null>(null);
  readonly isUIDesignGeneratingSignal = signal<boolean>(false);
  readonly uiDesignErrorSignal = signal<string | null>(null);
  readonly isWireframeGenerationCompleteSignal = signal<boolean>(false);

  // ===== Public Observables =====

  readonly isUIDesignMode = this.isUIDesignMode$.asObservable();
  readonly uiDesignNodes = this.uiDesignNodes$.asObservable();
  readonly selectedUIDesignNode = this.selectedUIDesignNode$.asObservable();
  readonly isUIDesignFullScreenOpen = this.isUIDesignFullScreenOpen$.asObservable();
  readonly uiDesignViewMode = this.uiDesignViewMode$.asObservable();
  readonly isUIDesignModalFullScreen = this.isUIDesignModalFullScreen$.asObservable();
  readonly isUIDesignCodeViewerOpen = this.isUIDesignCodeViewerOpen$.asObservable();
  readonly isUIDesignGenerating = this.isUIDesignGenerating$.asObservable();
  readonly uiDesignError = this.uiDesignError$.asObservable();
  readonly uiDesignApiInProgress = this.uiDesignApiInProgress$.asObservable();
  readonly isWireframeGenerationComplete = this.isWireframeGenerationComplete$.asObservable();
  readonly isUIDesignRegenerating = this.isUIDesignRegenerating$.asObservable();
  readonly uiDesignLoadingNodes = this.uiDesignLoadingNodes$.asObservable();
  readonly isUIDesignLoading = this.isUIDesignLoading$.asObservable();
  readonly showCanvasTooltip = this.showCanvasTooltip$.asObservable();
  readonly isEditingUIDesign = this.isEditingUIDesign$.asObservable();
  readonly introMessageState = this.introMessageState$.asObservable();
  readonly showUIDesignOverviewTab = this.showUIDesignOverviewTab$.asObservable();

  // ===== Internal State =====

  private currentActiveMessageId: string | null = null;
  private activeAIMessageIds = new Set<string>();
  private lightMessages: any[] = [];
  private destroy$ = new Subject<void>();

  constructor() {
    this.setupReactiveSubscriptions();
    this.subscribeToUIDesignService();
  }

  /**
   * Set up reactive subscriptions with proper cleanup
   */
  private setupReactiveSubscriptions(): void {
    // Sync BehaviorSubjects with signals
    this.isUIDesignMode$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(value => this.isUIDesignModeSignal.set(value));

    this.uiDesignNodes$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(nodes => this.uiDesignNodesSignal.set(nodes));

    this.selectedUIDesignNode$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(node => this.selectedUIDesignNodeSignal.set(node));

    this.isUIDesignGenerating$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(value => this.isUIDesignGeneratingSignal.set(value));

    this.uiDesignError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(error => this.uiDesignErrorSignal.set(error));

    this.isWireframeGenerationComplete$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(value => this.isWireframeGenerationCompleteSignal.set(value));

    // Initialize UI Design selection
    this.initializeUIDesignSelection();
  }

  /**
   * Subscribe to UI Design service for response handling
   */
  private subscribeToUIDesignService(): void {
    this.generateUIDesignService.uiDesignResponse$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(responseData => {
        if (responseData) {
          this.handleUIDesignResponseForOverview(responseData);
        }
      });

    this.generateUIDesignService.showOverviewTab$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(show => {
        this.showUIDesignOverviewTab$.next(show);
      });
  }

  /**
   * Initialize UI Design selection service
   */
  private initializeUIDesignSelection(): void {
    this.uiDesignSelectionService.reset();
    this.isEditingUIDesign$.next(false);

    this.uiDesignSelectionService.showCanvasTooltip
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(showTooltip => {
        this.showCanvasTooltip$.next(showTooltip);
      });

    this.uiDesignSelectionService.selectedNodes
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        // Update node selection visuals when selection changes
      });

    this.uiDesignSelectionService.selectedNode
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(selectedNode => {
        if (selectedNode) {
          // Handle single node selection
        }
      });

    this.uiDesignSelectionService.isEditingInProgress
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(isEditing => {
        this.isEditingUIDesign$.next(isEditing);
      });
  }

  // ===== State Getters =====

  get isUIDesignModeValue(): boolean {
    return this.isUIDesignMode$.value;
  }

  get selectedUIDesignNodeValue(): UIDesignNode | null {
    return this.selectedUIDesignNode$.value;
  }

  get uiDesignNodesValue(): UIDesignNode[] {
    return this.uiDesignNodes$.value;
  }

  get isUIDesignGeneratingValue(): boolean {
    return this.isUIDesignGenerating$.value;
  }

  get isUIDesignRegeneratingValue(): boolean {
    return this.isUIDesignRegenerating$.value;
  }

  get uiDesignApiInProgressValue(): boolean {
    return this.uiDesignApiInProgress$.value;
  }

  // ===== Public Methods for Component Integration =====

  /**
   * Set light messages for chat integration
   */
  setLightMessages(messages: any[]): void {
    this.lightMessages = messages;
  }

  /**
   * Get light messages for chat integration
   */
  getLightMessages(): any[] {
    return this.lightMessages;
  }

  /**
   * Set current active message ID
   */
  setCurrentActiveMessageId(messageId: string | null): void {
    this.currentActiveMessageId = messageId;
  }

  /**
   * Add active AI message ID
   */
  addActiveAIMessageId(messageId: string): void {
    this.activeAIMessageIds.add(messageId);
  }

  /**
   * Remove active AI message ID
   */
  removeActiveAIMessageId(messageId: string): void {
    this.activeAIMessageIds.delete(messageId);
  }

  /**
   * Check if message ID is active
   */
  hasActiveAIMessageId(messageId: string): boolean {
    return this.activeAIMessageIds.has(messageId);
  }

  /**
   * Set destroy subject for cleanup
   */
  setDestroySubject(destroy$: Subject<void>): void {
    this.destroy$ = destroy$;
  }

  /**
   * Cleanup service state
   */
  cleanup(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ===== UI Design Generation Methods =====

  /**
   * Initialize UI Design mode
   */
  initializeUIDesignMode(): void {
    this.isUIDesignMode$.next(true);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignRegenerating$.next(false);

    this.clearAllLoadingNodes();
    this.wireframeGenerationStateService.reset();
    this.initializeUIDesignChatMessages();
    this.initializeUIDesignSelection();
    this.startUIDesignGeneration();

    setTimeout(() => {
      this.setupAutoCanvasCentering();
    }, 200);
  }

  /**
   * Cleanup UI Design mode
   */
  cleanupUIDesignMode(): void {
    this.isUIDesignMode$.next(false);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isUIDesignRegenerating$.next(false);

    this.clearAllLoadingNodes();
    this.uiDesignIntroService.resetIntroState();

    this.uiDesignNodes$.next([]);
    this.selectedUIDesignNode$.next(null);
    this.isUIDesignFullScreenOpen$.next(false);

    this.uiDesignSelectionService.reset();
    this.showCanvasTooltip$.next(true);
    this.isEditingUIDesign$.next(false);
  }

  /**
   * Initialize UI Design chat messages
   */
  private initializeUIDesignChatMessages(): void {
    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryLoadingMessage =
        msg.text.includes('Generating') || msg.text.includes('Processing');
      const isAIMessage =
        msg.from === 'ai' &&
        msg.id &&
        (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      return !isTemporaryLoadingMessage || isAIMessage;
    });
  }

  /**
   * Start UI Design generation process
   */
  private startUIDesignGeneration(): void {
    this.isUIDesignGenerating$.next(true);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);

    this.uiDesignNodes$.next([]);
    this.createLoadingNodes();

    const uiDesignData = this.generateUIDesignService.getUIDesignData();

    if (uiDesignData) {
      this.initiateParallelUIDesignAPICalls(uiDesignData);
    } else {
      this.showUIDesignError('No UI Design data found. Please go back and submit a prompt.');
    }
  }

  /**
   * Create loading nodes for UI Design generation
   */
  private createLoadingNodes(): void {
    const loadingNodes: UIDesignNode[] = [
      {
        id: 'ui-design-loading-node',
        type: 'ui-design',
        data: {
          title: 'Generating UI Design...',
          htmlContent: this.sanitizer.bypassSecurityTrustHtml(''),
          rawContent: '',
          width: 300,
          height: 400,
          isLoading: true,
        },
        position: { x: 0, y: 0 },
        selected: false,
        dragging: false,
        visible: true,
      },
    ];

    this.uiDesignNodes$.next(loadingNodes);
  }

  /**
   * Initiate parallel UI Design API calls
   */
  private initiateParallelUIDesignAPICalls(uiDesignData: any): void {
    this.uiDesignApiInProgress$.next(true);
    this.uiDesignError$.next(null);

    this.wireframeGenerationStateService.startGeneration();

    const apiRequest = this.generateUIDesignService.buildAPIRequest();
    const mainAPICall = this.generateUIDesignService.generateUIDesign(apiRequest);
    const userRequest = uiDesignData.prompt || '';

    this.uiDesignIntroService
      .executeParallelGeneration(userRequest, mainAPICall, this.currentActiveMessageId || undefined)
      .subscribe({
        next: result => {
          if (result.mainAPISuccess) {
            this.handleUIDesignSuccess(result.mainAPIResult);
            this.uiDesignIntroService.completeTextReplacement();
          } else {
            this.handleUIDesignFailure(new Error('Main API failed'));
          }
        },
        error: error => {
          this.handleUIDesignFailure(error);
        },
      });

    this.uiDesignIntroService.introMessageState$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((state: IntroMessageState) => {
        this.introMessageState$.next(state);

        if (state.shouldReplaceText && state.targetMessageId && state.text) {
          this.handleChatMessageTextReplacement(state);
        }
      });
  }

  /**
   * Handle UI Design generation success
   */
  private handleUIDesignSuccess(response: any): void {
    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
    this.uiDesignError$.next(null);
    this.isUIDesignLoading$.next(false);

    this.restoreOriginalChatMessageText();

    try {
      if (this.isUIDesignResponse(response)) {
        this.processUIDesignResponse(response);
      } else {
        this.showUIDesignError('Invalid response format from wireframe generation API');
      }
    } catch (error) {
      this.showUIDesignError('Failed to process wireframe generation response');
    }
  }

  /**
   * Handle UI Design generation failure
   */
  private handleUIDesignFailure(error: any): void {
    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignLoading$.next(false);

    const errorMessage = error?.message || error?.error?.message || 'Wireframe generation failed';
    this.uiDesignError$.next(errorMessage);

    this.wireframeGenerationStateService.setError(errorMessage);
    this.showUIDesignError(errorMessage);
  }

  /**
   * Show UI Design error
   */
  private showUIDesignError(message: string): void {
    this.clearAllLoadingNodes();
    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);
    this.toastService.error(message);
  }

  /**
   * Handle UI Design response for overview tab
   */
  private handleUIDesignResponseForOverview(responseData: any): void {
    try {
      const pages: MobilePage[] = [];

      if (responseData.pages && Array.isArray(responseData.pages)) {
        responseData.pages.forEach((page: any) => {
          if (page.fileName && page.content) {
            pages.push({
              fileName: page.fileName,
              content: page.content,
            });
          }
        });
      }

      if (pages.length > 0) {
        this.processUIDesignResponse(responseData.pages);
      }
    } catch (error) {
      console.error('Error handling UI Design response for overview:', error);
    }
  }

  /**
   * Setup auto canvas centering
   */
  private setupAutoCanvasCentering(): void {
    // Update canvas container size and center on nodes
    setTimeout(() => {
      this.centerCanvasOnNodes();
    }, 100);
  }

  /**
   * Center canvas on nodes
   */
  centerCanvasOnNodes(): void {
    this.uiDesignViewportService.centerViewOnNodes();
  }

  /**
   * Handle chat message text replacement
   */
  private handleChatMessageTextReplacement(state: IntroMessageState): void {
    if (!state.targetMessageId || !state.text) {
      return;
    }

    if (!this.activeAIMessageIds.has(state.targetMessageId)) {
      return;
    }

    const targetMessage = this.lightMessages.find(msg => msg.id === state.targetMessageId);
    if (targetMessage) {
      if (!targetMessage.originalText) {
        targetMessage.originalText = targetMessage.text;
      }
      targetMessage.text = state.text;
    }
  }

  /**
   * Restore original chat message text
   */
  private restoreOriginalChatMessageText(): void {
    if (!this.currentActiveMessageId) {
      return;
    }

    const targetMessage = this.lightMessages.find(msg => msg.id === this.currentActiveMessageId);
    if (targetMessage && targetMessage.originalText) {
      targetMessage.text = targetMessage.originalText;
      delete targetMessage.originalText;
    }
  }

  /**
   * Clear all loading nodes (comprehensive cleanup)
   */
  private clearAllLoadingNodes(): void {
    // Clear regeneration loading nodes (separate BehaviorSubject)
    this.uiDesignLoadingNodes$.next([]);

    // Clear any loading nodes that might be in the main nodes array
    const currentNodes = this.uiDesignNodes$.value;
    const nonLoadingNodes = currentNodes.filter(
      node =>
        !node.data.isLoading &&
        !node.id.startsWith('loading-') &&
        node.id !== 'ui-design-loading-node'
    );

    // Only update if we actually removed loading nodes
    if (nonLoadingNodes.length !== currentNodes.length) {
      this.uiDesignNodes$.next(nonLoadingNodes);
    }
  }

  /**
   * Check if response is a valid UI Design or Wireframe response
   */
  private isUIDesignResponse(response: any): boolean {
    // Check for array format
    if (Array.isArray(response)) {
      return response.every(item => {
        if (typeof item !== 'object' || !item.content) {
          return false;
        }

        // Check for UI Design format: {"pageName": "...", "content": "..."}
        const hasPageName = 'pageName' in item;

        // Check for Wireframe format: {"fileName": "...", "content": "..."}
        const hasFileName = 'fileName' in item;

        return hasPageName || hasFileName;
      });
    }

    // Check for string format (JSON)
    if (typeof response === 'string') {
      try {
        const parsed = JSON.parse(response);
        return this.isUIDesignResponse(parsed);
      } catch {
        return false;
      }
    }

    return false;
  }

  /**
   * Process UI Design API response and create nodes
   */
  processUIDesignResponse(response: string | UIDesignAPIResponse[] | WireframeAPIResponse[]): void {
    // Clear all loading nodes immediately when processing response
    this.clearAllLoadingNodes();

    try {
      let pages: any[] = [];

      // Parse response if it's a string
      if (typeof response === 'string') {
        try {
          pages = JSON.parse(response);
        } catch (parseError) {
          this.showUIDesignError('Failed to parse response data');
          return;
        }
      } else if (Array.isArray(response)) {
        pages = response;
      } else {
        this.showUIDesignError('Invalid response format');
        return;
      }

      // Validate response structure
      if (!Array.isArray(pages) || pages.length === 0) {
        this.showUIDesignError('No pages found in response');
        return;
      }

      // Process pages using wireframe node management service
      this.processUIDesignPages(pages);

    } catch (error) {
      this.showUIDesignError(
        'Failed to create design nodes: ' +
          (error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  /**
   * Process UI Design pages and create nodes
   */
  private async processUIDesignPages(pages: any[]): Promise<void> {
    try {
      // Get current nodes for intelligent update/create decisions
      const currentNodes = this.uiDesignNodes$.value;

      // Convert pages to wireframe page data format
      const wireframePages = pages.map(page => ({
        fileName: page.fileName || page.pageName,
        content: page.content,
        pageName: page.pageName || page.fileName,
      }));

      // Use the robust node management service to process the response
      const result = await this.wireframeNodeManagementService.processRegenerationResponse(
        wireframePages,
        currentNodes,
        (count: number) => {
          // Simple positioning for now
          return Array.from({ length: count }, (_, index) => ({
            x: index * 350,
            y: 0
          }));
        }
      );

      // Update the nodes with the processed result
      this.uiDesignNodes$.next(result.updatedNodes);

      // Enable UI Design mode and disable history view
      this.isUIDesignMode$.next(true);

      // Calculate optimal viewport position to show all nodes
      if (result.updatedNodes.length > 0) {
        setTimeout(() => {
          this.centerCanvasOnNodes();
        }, 100);
      }

      // Create response data for overview tab
      const responseData = {
        pages: pages.map(page => ({
          fileName: page.fileName || page.pageName,
          content: page.content,
        })),
      };

      // Set UI Design response (cast to proper type)
      this.generateUIDesignService.setUIDesignResponse(responseData as any);

      // Update chat messages with success
      this.updateUIDesignChatMessagesWithSummary(result.summary);

      // Set nodes created state to show tooltip
      this.uiDesignSelectionService.setNodesCreated(true);

      // Set wireframe generation complete state to enable selection controls
      this.isWireframeGenerationComplete$.next(true);
      this.wireframeGenerationStateService.completeGeneration(result.updatedNodes.length);

    } catch (error) {
      this.showUIDesignError(
        'Failed to create design nodes: ' +
          (error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  /**
   * Update chat messages with enhanced operation summary
   */
  private updateUIDesignChatMessagesWithSummary(summary: any): void {
    // Filter out temporary loading messages but keep AI messages
    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryLoadingMessage =
        msg.text.includes('Generating') || msg.text.includes('Processing');
      const isAIMessage =
        msg.from === 'ai' &&
        msg.id &&
        (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      // Keep all AI messages, remove only temporary loading messages
      return !isTemporaryLoadingMessage || isAIMessage;
    });

    // Add success message if summary is provided
    if (summary && summary.message) {
      this.lightMessages.push({
        text: summary.message,
        from: 'ai',
        theme: 'light'
      });
    }
  }

  // ===== Modal Management Methods =====

  /**
   * Handle UI Design node double click
   */
  onUIDesignNodeDoubleClick(node: UIDesignNode): void {
    this.selectedUIDesignNode$.next(node);
    this.isUIDesignFullScreenOpen$.next(true);

    const userApplicationTarget = this.generateUIDesignService.getApplicationTarget();
    this.uiDesignViewMode$.next(userApplicationTarget || 'mobile');
  }

  /**
   * Close UI Design full screen modal
   */
  closeUIDesignFullScreen(): void {
    this.isUIDesignFullScreenOpen$.next(false);
    this.selectedUIDesignNode$.next(null);
  }

  /**
   * Switch UI Design view mode
   */
  switchUIDesignViewMode(mode: 'mobile' | 'web'): void {
    this.uiDesignViewMode$.next(mode);
  }

  /**
   * Toggle UI Design modal full screen
   */
  toggleUIDesignModalFullScreen(): void {
    const isCurrentlyFullScreen = this.isUIDesignModalFullScreen$.value;
    this.isUIDesignModalFullScreen$.next(!isCurrentlyFullScreen);
  }

  /**
   * Exit UI Design modal full screen
   */
  exitUIDesignModalFullScreen(): void {
    this.isUIDesignModalFullScreen$.next(false);
  }

  /**
   * Open UI Design in new tab
   */
  openUIDesignInNewTab(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      return;
    }

    try {
      const newWindow = window.open('', '_blank');

      if (newWindow) {
        newWindow.document.write(selectedNode.data.rawContent);
        newWindow.document.close();

        const title =
          selectedNode.data.displayTitle || selectedNode.data.title || 'UI Design Preview';
        newWindow.document.title = title;
      } else {
        this.openUIDesignWithBlobUrl(selectedNode.data.rawContent);
      }
    } catch (error) {
      this.openUIDesignWithBlobUrl(selectedNode.data.rawContent);
    }
  }

  /**
   * Open UI Design with blob URL (fallback method)
   */
  private openUIDesignWithBlobUrl(content: string): void {
    try {
      const blob = new Blob([content], { type: 'text/html' });
      const blobUrl = URL.createObjectURL(blob);

      const newWindow = window.open(blobUrl, '_blank');

      if (newWindow) {
        setTimeout(() => {
          URL.revokeObjectURL(blobUrl);
        }, 1000);
      }
    } catch (error) {
      this.toastService.error('Failed to open UI Design in new tab');
    }
  }

  /**
   * Close UI Design code viewer
   */
  closeUIDesignCodeViewer(): void {
    this.isUIDesignCodeViewerOpen$.next(false);
  }

  // ===== Loading State Methods =====

  /**
   * Should show UI Design loading indicator
   */
  shouldShowUIDesignLoadingIndicator(): boolean {
    const isUIDesignMode = this.isUIDesignMode$.value;
    const isGenerating = this.isUIDesignGenerating$.value;
    const isRegenerating = this.isUIDesignRegenerating$.value;
    const isApiInProgress = this.uiDesignApiInProgress$.value;

    return isUIDesignMode && (isGenerating || isRegenerating || isApiInProgress);
  }

  /**
   * Get loading text for nodes
   */
  getLoadingText(title: string): string {
    if (title.includes('Editing') && title.includes('...')) {
      return title;
    }
    return 'Generating wireframe...';
  }

  // ===== Canvas Control Methods =====

  /**
   * Center canvas on nodes with specific viewport
   */
  centerCanvasOnNodesWithViewport(viewport: { x: number; y: number; zoom: number }): void {
    this.uiDesignCanvasService.updateViewport({
      x: viewport.x,
      y: viewport.y,
      zoom: viewport.zoom,
    });
  }

  // ===== Utility Methods =====

  /**
   * Track by function for UI Design nodes
   */
  trackByUIDesignNode(_index: number, node: UIDesignNode): string {
    return node.id;
  }

  /**
   * Handle wireframe generation command
   */
  handleWireframeGenerationCommand(prompt: string, chatWindow: any): void {
    console.info('🎯 Handling /wireframe-generation command');

    // Extract the actual request after the command
    const userRequest = prompt.replace(/^\/wireframe-generation\s*/i, '').trim() || 'Generate a wireframe';

    // Add user message to chat
    this.lightMessages.push({
      text: prompt,
      from: 'user',
      theme: 'light'
    });

    // Use the UI design intro service with shimmer loading
    this.uiDesignIntroService.createAndDisplayIntroCardWithShimmer(
      userRequest,
      chatWindow
    ).subscribe({
      next: (messageId) => {
        console.info('✅ Wireframe generation command completed successfully:', messageId);
      },
      error: (error) => {
        console.error('❌ Wireframe generation command failed:', error);
        this.toastService.error('Failed to generate wireframe intro message. Please try again.');
      }
    });
  }

  // ===== Edit and Regeneration Methods =====

  /**
   * Handle UI Design edit prompt
   */
  onUIDesignEditPrompt(prompt: string): void {
    if (!this.isUIDesignMode$.value) {
      return;
    }

    // Check for special commands
    if (this.handleSpecialCommands(prompt)) {
      return;
    }

    const selectedNodes = this.uiDesignSelectionService.getSelectedNodes();

    if (selectedNodes.length === 0) {
      this.toastService.error('Please select at least one design to edit');
      return;
    }

    // Set regeneration state
    this.isUIDesignRegenerating$.next(true);
    this.isUIDesignLoading$.next(true);

    // Create loading nodes for selected nodes
    this.createLoadingNodesForSelectedNodes(selectedNodes);

    // Build edit request
    const editRequest = this.uiDesignEditService.buildEditRequest(selectedNodes, prompt);

    // Execute parallel regeneration
    const mainEditAPICall = this.uiDesignEditService.editUIDesignPage(editRequest);

    this.uiDesignIntroService
      .executeParallelRegeneration(
        prompt,
        selectedNodes,
        mainEditAPICall,
        this.currentActiveMessageId || undefined
      )
      .subscribe({
        next: result => {
          if (result.mainAPISuccess) {
            this.handleEditSuccess(result.mainAPIResult, selectedNodes);
          } else {
            this.handleEditFailure('Main edit API failed');
          }
        },
        error: _error => {
          this.handleEditFailure('Regeneration API calls failed');
        }
      });
  }

  /**
   * Handle special commands
   */
  private handleSpecialCommands(prompt: string): boolean {
    // Handle wireframe generation command
    if (prompt.toLowerCase().startsWith('/wireframe-generation')) {
      // This would need chatWindow reference - handled by component
      return true;
    }
    return false;
  }

  /**
   * Create loading nodes for selected nodes
   */
  private createLoadingNodesForSelectedNodes(selectedNodes: MultiSelectedNodeData[]): void {
    const loadingNodes: UIDesignNode[] = [];

    selectedNodes.forEach((selectedNode, index) => {
      const existingNode = this.uiDesignNodes$.value.find(node => node.id === selectedNode.nodeId);
      if (!existingNode) return;

      const loadingMessage = `Editing ${selectedNode.fileName}...`;

      // Create loading node with same position and dimensions as original
      const loadingNode: UIDesignNode = {
        id: `loading-${selectedNode.nodeId}-${Date.now()}-${index}`,
        type: 'ui-design',
        data: {
          title: selectedNode.fileName,
          displayTitle: selectedNode.fileName,
          htmlContent: '',
          rawContent: '',
          width: existingNode.data.width,
          height: existingNode.data.height,
          isLoading: true,
          loadingMessage: loadingMessage,
          originalNodeId: selectedNode.nodeId,
        },
        position: { ...existingNode.position },
        selected: false,
        dragging: false,
        visible: true,
      };

      loadingNodes.push(loadingNode);
    });

    // Update loading nodes state
    this.uiDesignLoadingNodes$.next(loadingNodes);
  }

  /**
   * Handle edit success
   */
  private handleEditSuccess(response: any, selectedNodes: MultiSelectedNodeData[]): void {
    // Validate response
    const validatedResponse = this.validateEditResponse(response);
    if (!validatedResponse) {
      this.handleEditFailure('Invalid response format from edit API');
      return;
    }

    // Update multiple nodes based on response
    this.updateMultipleNodesContent(validatedResponse, selectedNodes);

    // Update chat messages
    const fileNames = selectedNodes.map(node => node.fileName);
    this.updateEditChatMessages(true, fileNames);

    // Restore original text in chat messages if text replacement was used
    this.restoreOriginalChatMessageText();

    // Clear editing and regeneration state
    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);
  }

  /**
   * Handle edit failure
   */
  private handleEditFailure(errorMessage: string): void {
    // Clear ALL loading nodes
    this.clearAllLoadingNodes();

    // Update chat messages
    this.updateEditChatMessages(false, '', errorMessage);

    // Clear editing and regeneration state
    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);

    // Show error toast
    this.toastService.error(errorMessage);
  }

  /**
   * Validate edit response
   */
  private validateEditResponse(response: any): any[] | null {
    if (!response) return null;

    // Handle different response formats
    if (Array.isArray(response)) {
      return response.filter(item => item && item.fileName && item.content);
    }

    if (typeof response === 'object' && response.files && Array.isArray(response.files)) {
      return response.files.filter((item: any) => item && item.fileName && item.content);
    }

    return null;
  }

  /**
   * Update multiple nodes content
   */
  private updateMultipleNodesContent(
    validatedResponse: any[],
    selectedNodes: MultiSelectedNodeData[]
  ): void {
    const currentNodes = this.uiDesignNodes$.value;
    let updatedCount = 0;

    // Create a map of fileName to response content for quick lookup
    const responseMap = new Map<string, string>();
    validatedResponse.forEach(file => {
      responseMap.set(file.fileName, file.content);
    });

    // Update nodes with new content
    const updatedNodes = currentNodes.map(node => {
      const selectedNode = selectedNodes.find(selected => selected.nodeId === node.id);
      if (!selectedNode) return node;

      const newContent = responseMap.get(selectedNode.fileName);
      if (!newContent) return node;

      updatedCount++;
      return {
        ...node,
        data: {
          ...node.data,
          rawContent: newContent,
          htmlContent: this.sanitizer.bypassSecurityTrustHtml(newContent),
        },
      };
    });

    this.uiDesignNodes$.next(updatedNodes);
  }

  /**
   * Update chat messages for edit operations
   */
  private updateEditChatMessages(
    success: boolean,
    fileNames: string | string[],
    _errorMessage?: string
  ): void {
    // Filter out temporary editing messages but keep AI messages
    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryEditingMessage =
        msg.text.includes('editing') ||
        msg.text.includes('Please wait') ||
        msg.text.includes('Hang tight') ||
        msg.text.includes('Editing');
      const isAIMessage =
        msg.from === 'ai' &&
        msg.id &&
        (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      return !isTemporaryEditingMessage || isAIMessage;
    });

    if (success) {
      const fileNamesArray = Array.isArray(fileNames) ? fileNames : [fileNames];
      const message = fileNamesArray.length === 1
        ? `✅ Successfully updated ${fileNamesArray[0]}`
        : `✅ Successfully updated ${fileNamesArray.length} files`;

      this.lightMessages.push({
        text: message,
        from: 'ai',
        theme: 'light'
      });
    }
  }
}
