import { Injectable, inject } from '@angular/core';
import { Observable, from, of, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import JSZip from 'jszip';
import { createLogger } from '../../utils/logger';
import { ToastService } from '../toast.service';
import { GenerationStateService } from '../generation-state.service';

export interface VSCodeExportOptions {
  projectName: string;
  files: any[];
  openInVSCode?: boolean;
  downloadFallback?: boolean;
}

export interface VSCodeExportResult {
  success: boolean;
  method: 'vscode-protocol' | 'download-fallback' | 'manual-instructions' | 'file-system-api' | 'web-integration';
  message: string;
  downloadUrl?: string;
  downloadFileName?: string;
  projectPath?: string;
}

@Injectable({
  providedIn: 'root'
})
export class VSCodeExportService {
  private readonly logger = createLogger('VSCodeExport');
  private readonly toastService = inject(ToastService);
  private readonly generationStateService = inject(GenerationStateService);

  // Browser and device detection
  private readonly browserInfo = this.detectBrowser();
  private readonly deviceInfo = this.detectDevice();

  /**
   * Export project to VSCode using multiple fallback methods
   */
  exportToVSCode(options: VSCodeExportOptions): Observable<VSCodeExportResult> {
    this.logger.info('🚀 Starting VSCode export process', options);

    return this.validateExportData(options).pipe(
      switchMap(() => this.attemptVSCodeProtocol(options)),
      catchError(error => {
        this.logger.warn('VSCode protocol failed, trying fallback methods', error);
        return this.handleFallbackMethods(options);
      })
    );
  }

  /**
   * Validate export data before processing
   */
  private validateExportData(options: VSCodeExportOptions): Observable<boolean> {
    if (!options.files || options.files.length === 0) {
      const error = 'No files available for export';
      this.logger.error(error);
      return throwError(() => new Error(error));
    }

    if (!options.projectName || options.projectName.trim() === '') {
      const error = 'Project name is required for export';
      this.logger.error(error);
      return throwError(() => new Error(error));
    }

    this.logger.info('✅ Export data validation passed');
    return of(true);
  }

  /**
   * Attempt to open VSCode using multiple direct integration methods
   * ENHANCED: Multiple approaches to directly open VSCode with project
   */
  private attemptVSCodeProtocol(options: VSCodeExportOptions): Observable<VSCodeExportResult> {
    return new Observable(observer => {
      try {
        this.logger.info('🔗 Attempting direct VSCode integration');

        // Method 1: Try vscode:// protocol with file URIs
        this.tryVSCodeProtocolDirect(options)
          .then(success => {
            if (success) {
              observer.next({
                success: true,
                method: 'vscode-protocol',
                message: 'VSCode opened successfully with project files!'
              });
              observer.complete();
              return Promise.resolve(true);
            } else {
              // Method 2: Try VSCode web integration
              return this.tryVSCodeWebIntegration(options);
            }
          })
          .then(webSuccess => {
            if (webSuccess === true) {
              observer.next({
                success: true,
                method: 'vscode-protocol',
                message: 'VSCode web opened with project!'
              });
              observer.complete();
              return Promise.resolve(true);
            } else if (webSuccess === false) {
              // Method 3: Try local VSCode command
              return this.tryLocalVSCodeCommand(options);
            }
            return Promise.resolve(false);
          })
          .then(localSuccess => {
            if (localSuccess === true) {
              observer.next({
                success: true,
                method: 'vscode-protocol',
                message: 'VSCode opened locally with project!'
              });
              observer.complete();
            } else if (localSuccess === false) {
              // All methods failed, use fallback
              observer.error(new Error('All VSCode direct methods failed - using fallback'));
            }
          })
          .catch(error => {
            this.logger.error('VSCode protocol attempts failed:', error);
            observer.error(error);
          });

      } catch (error) {
        this.logger.error('Error attempting VSCode protocol:', error);
        observer.error(error);
      }
    });
  }

  /**
   * Handle fallback methods when VSCode protocol fails
   */
  private handleFallbackMethods(options: VSCodeExportOptions): Observable<VSCodeExportResult> {
    this.logger.info('🔄 Using fallback method: download + instructions');

    return this.createDownloadableProject(options).pipe(
      map(downloadUrl => {
        // Get the downloaded filename for better guidance
        const downloadedFileName = (options as any).downloadedFileName || `${options.projectName.toLowerCase()}.zip`;

        // Provide guidance based on VSCode detection
        this.detectVSCodeAndProvideGuidance(options, downloadedFileName);

        return {
          success: true,
          method: 'download-fallback' as const,
          message: `Project downloaded as ${downloadedFileName}. Extract and open in VSCode.`,
          downloadUrl,
          downloadFileName: downloadedFileName,
          projectPath: options.projectName
        };
      }),
      catchError(error => {
        this.logger.error('Fallback download failed:', error);
        this.showVSCodeInstallationGuidance();
        return of({
          success: false,
          method: 'manual-instructions' as const,
          message: 'Download failed. Please try again or copy files manually.'
        });
      })
    );
  }

  /**
   * Create workspace configuration for VSCode
   */
  private createWorkspaceConfig(options: VSCodeExportOptions): any {
    return {
      folders: [
        {
          name: options.projectName,
          path: `./${options.projectName}`
        }
      ],
      settings: {
        "typescript.preferences.includePackageJsonAutoImports": "auto",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.organizeImports": true
        }
      },
      extensions: {
        recommendations: [
          "angular.ng-template",
          "ms-vscode.vscode-typescript-next",
          "esbenp.prettier-vscode",
          "bradlc.vscode-tailwindcss"
        ]
      }
    };
  }

  /**
   * Generate VSCode URL for opening the project
   * Note: This is a placeholder since direct VSCode opening isn't supported from browser
   */
  private generateVSCodeUrl(workspaceConfig: any): string {
    // This is a placeholder - actual VSCode opening requires local file system access
    return `vscode://file/project-workspace`;
  }

  /**
   * Create downloadable project as fallback
   */
  private createDownloadableProject(options: VSCodeExportOptions): Observable<string> {
    return from(this.generateProjectZip(options)).pipe(
      map(blob => {
        const url = URL.createObjectURL(blob);
        const downloadedFileName = this.triggerDownload(blob, options.projectName);

        // Store the downloaded filename for guidance messages
        (options as any).downloadedFileName = downloadedFileName;

        return url;
      })
    );
  }

  /**
   * Generate project zip file
   */
  private async generateProjectZip(options: VSCodeExportOptions): Promise<Blob> {
    const zip = new JSZip();
    const projectFolder = zip.folder(options.projectName);

    if (!projectFolder) {
      throw new Error('Failed to create project folder');
    }

    // Add all project files
    for (const file of options.files) {
      const fileName = file.fileName || file.name || file.path || 'unknown.txt';
      const content = file.content || '';
      this.addFileToZipWithPath(projectFolder, fileName, content);
    }

    // Add VSCode workspace file
    const workspaceConfig = this.createWorkspaceConfig(options);
    projectFolder.file(`${options.projectName}.code-workspace`, JSON.stringify(workspaceConfig, null, 2));

    // Add README with instructions
    const readmeContent = this.generateVSCodeReadme(options.projectName);
    projectFolder.file('README.md', readmeContent);

    return await zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: { level: 6 }
    });
  }

  /**
   * Add file to zip with proper path structure
   */
  private addFileToZipWithPath(folder: JSZip, filePath: string, content: string): void {
    const pathParts = filePath.split('/');
    let currentFolder = folder;

    // Create nested folders if needed
    for (let i = 0; i < pathParts.length - 1; i++) {
      const folderName = pathParts[i];
      if (folderName) {
        currentFolder = currentFolder.folder(folderName) || currentFolder;
      }
    }

    // Add the file
    const fileName = pathParts[pathParts.length - 1];
    currentFolder.file(fileName, content);
  }

  /**
   * Trigger download of the zip file with cross-browser compatibility
   */
  private triggerDownload(blob: Blob, projectName: string): string {
    // Normalize project name to lowercase for consistency
    const normalizedName = projectName.toLowerCase().replace(/[^a-z0-9-]/g, '-');
    const fileName = `${normalizedName}.zip`;

    this.logger.info('📥 Triggering download with cross-browser compatibility', {
      originalName: projectName,
      normalizedName,
      fileName,
      browserInfo: this.browserInfo,
      deviceInfo: this.deviceInfo
    });

    try {
      // Method 1: Modern browsers with download attribute support
      if (this.supportsDownloadAttribute()) {
        return this.downloadWithLinkElement(blob, fileName);
      }

      // Method 2: Fallback for older browsers
      if (this.browserInfo.isIE || this.browserInfo.isEdgeLegacy) {
        return this.downloadForIE(blob, fileName);
      }

      // Method 3: Mobile browsers fallback
      if (this.deviceInfo.isMobile) {
        return this.downloadForMobile(blob, fileName);
      }

      // Method 4: Universal fallback
      return this.downloadUniversalFallback(blob, fileName);

    } catch (error) {
      this.logger.error('Download failed, trying universal fallback:', error);
      return this.downloadUniversalFallback(blob, fileName);
    }
  }

  /**
   * Generate README content with VSCode instructions
   */
  private generateVSCodeReadme(projectName: string): string {
    return `# ${projectName}

## Opening in VSCode

This project has been exported for use with Visual Studio Code.

### Method 1: Using the Workspace File
1. Open VSCode
2. Go to File → Open Workspace from File
3. Select the \`${projectName}.code-workspace\` file
4. VSCode will open the project with recommended settings

### Method 2: Open Folder
1. Open VSCode
2. Go to File → Open Folder
3. Select the project folder
4. Install recommended extensions when prompted

### Recommended Extensions
- Angular Language Service
- TypeScript Importer
- Prettier - Code formatter
- Tailwind CSS IntelliSense

### Getting Started
1. Open a terminal in VSCode
2. Run \`npm install\` to install dependencies
3. Run \`npm start\` or \`ng serve\` to start the development server

Happy coding! 🚀
`;
  }

  /**
   * Method 1: Try direct vscode:// protocol with File System Access API
   * ENHANCED: Attempt to create real files and open VSCode directly
   */
  private async tryVSCodeProtocolDirect(options: VSCodeExportOptions): Promise<boolean> {
    try {
      this.logger.info('🔗 Trying direct VSCode protocol with File System Access API');

      // Check if File System Access API is supported
      if ('showDirectoryPicker' in window) {
        const result = await this.tryFileSystemAccessAPI(options);
        if (result) {
          this.toastService.success('✅ Project files created! VSCode should open automatically.');
          return true;
        }
      }

      // Try legacy protocol as fallback
      const legacyResult = await this.tryLegacyVSCodeProtocol(options);
      if (legacyResult) {
        this.toastService.info('📂 VSCode protocol attempted. If VSCode didn\'t open, please check your installation.');
        return true;
      }

      return false;

    } catch (error) {
      this.logger.error('Direct VSCode protocol failed:', error);
      return false;
    }
  }

  /**
   * Try File System Access API for direct file creation
   */
  private async tryFileSystemAccessAPI(options: VSCodeExportOptions): Promise<boolean> {
    try {
      this.logger.info('📁 Using File System Access API');

      // Show user-friendly message
      this.toastService.info('Please select a folder to create your project...');

      // Request directory access from user
      const directoryHandle = await (window as any).showDirectoryPicker({
        mode: 'readwrite',
        startIn: 'documents'
      });

      // Create project folder
      const projectFolderHandle = await directoryHandle.getDirectoryHandle(options.projectName, {
        create: true
      });

      // Write all project files
      for (const file of options.files) {
        const fileName = file.name || file.path || 'unknown.txt';
        const fileContent = file.content || '';

        // Handle nested directories
        const pathParts = fileName.split('/');
        let currentDir = projectFolderHandle;

        // Create nested directories if needed
        for (let i = 0; i < pathParts.length - 1; i++) {
          const dirName = pathParts[i];
          if (dirName) {
            currentDir = await currentDir.getDirectoryHandle(dirName, { create: true });
          }
        }

        // Create the file
        const actualFileName = pathParts[pathParts.length - 1];
        const fileHandle = await currentDir.getFileHandle(actualFileName, { create: true });
        const writable = await fileHandle.createWritable();
        await writable.write(fileContent);
        await writable.close();
      }

      // Create VSCode workspace file
      const workspaceConfig = this.createWorkspaceConfig(options);
      const workspaceHandle = await projectFolderHandle.getFileHandle(
        `${options.projectName}.code-workspace`,
        { create: true }
      );
      const workspaceWritable = await workspaceHandle.createWritable();
      await workspaceWritable.write(JSON.stringify(workspaceConfig, null, 2));
      await workspaceWritable.close();

      // Create README
      const readmeContent = this.generateVSCodeReadme(options.projectName);
      const readmeHandle = await projectFolderHandle.getFileHandle('README.md', { create: true });
      const readmeWritable = await readmeHandle.createWritable();
      await readmeWritable.write(readmeContent);
      await readmeWritable.close();

      this.logger.info('✅ Files created successfully using File System Access API');

      // Now try to open VSCode with the actual project path
      const projectPath = await this.getDirectoryPath(projectFolderHandle);
      return await this.openVSCodeWithPath(projectPath);

    } catch (error: any) {
      if (error?.name === 'AbortError') {
        this.logger.info('❌ User cancelled directory selection');
        this.toastService.info('Directory selection cancelled. Using download fallback.');
      } else {
        this.logger.error('File System Access API failed:', error);
      }
      return false;
    }
  }

  /**
   * Try legacy VSCode protocol without file system access
   */
  private async tryLegacyVSCodeProtocol(options: VSCodeExportOptions): Promise<boolean> {
    try {
      this.logger.info('🔗 Using legacy VSCode protocol');

      // Since we cannot create arbitrary file paths from browser,
      // we'll just try to open VSCode itself without a specific path
      const protocols = [
        'vscode://',           // Just open VSCode
        'vscode-insiders://',  // VSCode Insiders
        'code://'              // Alternative protocol
      ];

      for (const protocol of protocols) {
        try {
          // Don't append project name as it creates invalid paths
          const vscodeUrl = protocol;

          // Create hidden link and attempt to open
          const link = document.createElement('a');
          link.href = vscodeUrl;
          link.style.display = 'none';
          document.body.appendChild(link);

          // Set up detection for successful opening
          const success = await new Promise<boolean>((resolve) => {
            const timeout = setTimeout(() => {
              document.body.removeChild(link);
              resolve(false);
            }, 2000);

            // Listen for window focus change
            const handleVisibilityChange = () => {
              if (document.hidden) {
                clearTimeout(timeout);
                document.removeEventListener('visibilitychange', handleVisibilityChange);
                document.body.removeChild(link);
                resolve(true);
              }
            };

            document.addEventListener('visibilitychange', handleVisibilityChange);

            // Trigger the protocol
            link.click();
          });

          if (success) {
            this.logger.info(`✅ VSCode protocol worked: ${protocol}`);
            this.toastService.success('VSCode opened! Use File → Open Folder to open your downloaded project.');
            return true;
          }

        } catch (protocolError) {
          this.logger.info(`❌ Protocol failed: ${protocol}`, protocolError);
          continue;
        }
      }

      return false;

    } catch (error) {
      this.logger.error('Legacy VSCode protocol failed:', error);
      return false;
    }
  }

  /**
   * Get directory path from directory handle (if possible)
   */
  private async getDirectoryPath(directoryHandle: any): Promise<string> {
    try {
      // This is a simplified approach - actual path extraction is limited in browsers
      return directoryHandle.name || 'project-directory';
    } catch (error) {
      return 'project-directory';
    }
  }

  /**
   * Open VSCode with specific path
   */
  private async openVSCodeWithPath(projectPath: string): Promise<boolean> {
    try {
      // Validate that we have a meaningful path
      if (!projectPath || projectPath === 'project-directory') {
        this.logger.warn('Invalid or generic project path provided, skipping VSCode protocol');
        return false;
      }

      // Only attempt VSCode protocol if we have a valid absolute path
      // Browser-based applications cannot create arbitrary file system paths
      if (!projectPath.startsWith('/') && !projectPath.match(/^[A-Za-z]:\\/)) {
        this.logger.warn('Project path is not absolute, cannot use VSCode protocol:', projectPath);
        return false;
      }

      const vscodeUrl = `vscode://file/${encodeURIComponent(projectPath)}`;
      this.logger.info('Attempting to open VSCode with URL:', vscodeUrl);

      // Try to open VSCode
      const vscodeWindow = window.open(vscodeUrl, '_blank');

      if (vscodeWindow) {
        this.toastService.success('VSCode should open with your project!');
        return true;
      }

      // Fallback: try with iframe
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = vscodeUrl;
      document.body.appendChild(iframe);

      setTimeout(() => {
        if (document.body.contains(iframe)) {
          document.body.removeChild(iframe);
        }
      }, 3000);

      this.toastService.info('VSCode command sent! If VSCode didn\'t open, please check your installation.');
      return true;

    } catch (error) {
      this.logger.error('Failed to open VSCode with path:', error);
      return false;
    }
  }

  /**
   * Method 2: Try VSCode web integration
   * ENHANCED: Open VSCode in web browser with project files
   */
  private async tryVSCodeWebIntegration(options: VSCodeExportOptions): Promise<boolean> {
    try {
      this.logger.info('🌐 Trying VSCode web integration');

      // Create project files as data URIs
      const projectFiles = await this.createProjectDataURIs(options);

      // Try to open VSCode web with project data
      const vscodeWebUrl = this.generateVSCodeWebUrl(projectFiles, options.projectName);

      // Open VSCode web in new tab
      const vscodeWindow = window.open(vscodeWebUrl, '_blank', 'noopener,noreferrer');

      if (vscodeWindow) {
        this.logger.info('✅ VSCode web opened successfully');
        this.toastService.success('VSCode web opened with your project!');
        return true;
      } else {
        this.logger.info('❌ VSCode web popup blocked');
        return false;
      }

    } catch (error) {
      this.logger.error('VSCode web integration failed:', error);
      return false;
    }
  }

  /**
   * Method 3: Try local VSCode command execution
   * ENHANCED: Attempt to trigger local VSCode installation with better detection
   */
  private async tryLocalVSCodeCommand(options: VSCodeExportOptions): Promise<boolean> {
    try {
      this.logger.info('💻 Trying local VSCode command');

      // Create downloadable project first
      const projectBlob = await this.generateProjectZip(options);

      // Always trigger download as primary method with normalized filename
      const downloadedFileName = this.triggerDownload(projectBlob, options.projectName);

      // Since we're in a browser environment, we cannot create arbitrary file paths
      // The best we can do is try to open VSCode itself and let the user manually open the downloaded project
      const vscodeCommands = [
        'vscode://',  // Just open VSCode without a specific path
        'code://',    // Alternative VSCode protocol
        'vscode-insiders://'  // VSCode Insiders
      ];

      let commandAttempted = false;

      for (const command of vscodeCommands) {
        try {
          // Create iframe to attempt command execution
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          iframe.src = command;
          document.body.appendChild(iframe);

          // Clean up after attempt
          setTimeout(() => {
            if (document.body.contains(iframe)) {
              document.body.removeChild(iframe);
            }
          }, 2000);

          this.logger.info(`✅ VSCode protocol attempted: ${command}`);
          commandAttempted = true;

          // Only try the first successful command
          break;

        } catch (cmdError) {
          this.logger.info(`❌ Command failed: ${command}`, cmdError);
          continue;
        }
      }

      if (commandAttempted) {
        this.toastService.success(`📦 Project downloaded as ${downloadedFileName}! VSCode opening attempted.`);
        this.toastService.info(`💡 If VSCode opened: Extract ${downloadedFileName} → File → Open Folder → Select extracted folder`);
        return true;
      }

      // If no commands worked, still return true since we successfully downloaded the project
      this.toastService.success(`📦 Project downloaded as ${downloadedFileName}!`);
      this.toastService.info(`💡 Extract ${downloadedFileName} and open the folder in VSCode manually`);
      return true;

    } catch (error) {
      this.logger.error('Local VSCode command failed:', error);
      return false;
    }
  }

  /**
   * Create in-memory project structure
   */
  private async createInMemoryProject(options: VSCodeExportOptions): Promise<{ tempPath: string; files: any[] }> {
    // Since we can't actually write to filesystem from browser,
    // we'll create a virtual representation
    const tempPath = `/tmp/${options.projectName}-${Date.now()}`;

    return {
      tempPath,
      files: options.files
    };
  }

  /**
   * Create project files as data URIs
   */
  private async createProjectDataURIs(options: VSCodeExportOptions): Promise<any[]> {
    return options.files.map(file => ({
      name: file.name || file.path,
      content: file.content,
      dataUri: `data:text/plain;base64,${btoa(file.content || '')}`
    }));
  }

  /**
   * Generate VSCode web URL with project data
   */
  private generateVSCodeWebUrl(projectFiles: any[], projectName: string): string {
    // Use VSCode web service (vscode.dev) with project data
    const baseUrl = 'https://vscode.dev/';

    // For now, open VSCode web and let user import manually
    // In future, could integrate with GitHub Codespaces or similar
    return baseUrl;
  }

  /**
   * Detect if VSCode is likely installed and provide appropriate guidance
   */
  private async detectVSCodeAndProvideGuidance(options: VSCodeExportOptions, downloadedFileName: string): Promise<void> {
    try {
      // Test if VSCode protocol is registered (without invalid file path)
      const testUrl = 'vscode://';
      const testResult = await this.testVSCodeProtocol(testUrl);

      if (testResult) {
        this.toastService.success('✅ VSCode detected! Your project is ready.');
        this.toastService.info(`📁 Extract ${downloadedFileName} and open the folder in VSCode`);
        this.toastService.info('💡 For best experience: Open the .code-workspace file included in the download');
      } else {
        this.toastService.info(`📦 Project downloaded as ${downloadedFileName}`);
        this.showVSCodeInstallationGuidanceWithFilename(downloadedFileName);
      }
    } catch (error) {
      this.logger.error('Error detecting VSCode:', error);
      this.showVSCodeInstallationGuidanceWithFilename(downloadedFileName);
    }
  }

  /**
   * Test if VSCode protocol is available
   */
  private async testVSCodeProtocol(testUrl: string): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = testUrl;

        let resolved = false;

        // If protocol works, it should not trigger an error
        iframe.onload = () => {
          if (!resolved) {
            resolved = true;
            resolve(true);
          }
        };

        iframe.onerror = () => {
          if (!resolved) {
            resolved = true;
            resolve(false);
          }
        };

        document.body.appendChild(iframe);

        // Clean up and resolve false if no response within 2 seconds
        setTimeout(() => {
          if (document.body.contains(iframe)) {
            document.body.removeChild(iframe);
          }
          if (!resolved) {
            resolved = true;
            resolve(false);
          }
        }, 2000);

      } catch (error) {
        resolve(false);
      }
    });
  }

  /**
   * Show VSCode installation and setup guidance
   */
  private showVSCodeInstallationGuidance(downloadedFileName?: string): void {
    this.toastService.info('💻 VSCode not detected. Download it from https://code.visualstudio.com/');

    setTimeout(() => {
      if (downloadedFileName) {
        this.toastService.info(`📁 After installing VSCode: Extract ${downloadedFileName} → Open VSCode → File → Open Folder`);
      } else {
        this.toastService.info('📁 After installing VSCode: Extract the zip → Open VSCode → File → Open Folder');
      }
    }, 3000);

    setTimeout(() => {
      this.toastService.info('🔧 For best experience: Open the .code-workspace file included in your download');
    }, 6000);
  }

  /**
   * Show VSCode installation guidance with specific filename
   */
  private showVSCodeInstallationGuidanceWithFilename(downloadedFileName: string): void {
    this.showVSCodeInstallationGuidance(downloadedFileName);
  }

  // ============================================================================
  // BROWSER AND DEVICE DETECTION METHODS
  // ============================================================================

  /**
   * Detect browser information
   */
  private detectBrowser() {
    const userAgent = navigator.userAgent.toLowerCase();
    const isChrome = userAgent.includes('chrome') && !userAgent.includes('edg');
    const isFirefox = userAgent.includes('firefox');
    const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome');
    const isEdge = userAgent.includes('edg');
    const isEdgeLegacy = userAgent.includes('edge/');
    const isIE = userAgent.includes('msie') || userAgent.includes('trident/');
    const isOpera = userAgent.includes('opera') || userAgent.includes('opr/');

    return {
      isChrome,
      isFirefox,
      isSafari,
      isEdge,
      isEdgeLegacy,
      isIE,
      isOpera,
      userAgent,
      supportsFileSystemAPI: 'showDirectoryPicker' in window,
      supportsDownloadAttribute: 'download' in document.createElement('a')
    };
  }

  /**
   * Detect device information
   */
  private detectDevice() {
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
    const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent);
    const isDesktop = !isMobile && !isTablet;
    const isIOS = /iphone|ipad|ipod/i.test(userAgent);
    const isAndroid = /android/i.test(userAgent);

    return {
      isMobile,
      isTablet,
      isDesktop,
      isIOS,
      isAndroid,
      touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0
    };
  }

  // ============================================================================
  // CROSS-BROWSER DOWNLOAD METHODS
  // ============================================================================

  /**
   * Check if browser supports download attribute
   */
  private supportsDownloadAttribute(): boolean {
    return this.browserInfo.supportsDownloadAttribute && !this.browserInfo.isIE && !this.browserInfo.isEdgeLegacy;
  }

  /**
   * Download using modern link element with download attribute
   */
  private downloadWithLinkElement(blob: Blob, fileName: string): string {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL after a delay
    setTimeout(() => URL.revokeObjectURL(url), 1000);

    this.logger.info('✅ Download triggered using link element method');
    return fileName;
  }

  /**
   * Download for Internet Explorer
   */
  private downloadForIE(blob: Blob, fileName: string): string {
    if ((window.navigator as any).msSaveBlob) {
      (window.navigator as any).msSaveBlob(blob, fileName);
      this.logger.info('✅ Download triggered using IE msSaveBlob method');
      return fileName;
    }

    // Fallback to universal method
    return this.downloadUniversalFallback(blob, fileName);
  }

  /**
   * Download for mobile browsers
   */
  private downloadForMobile(blob: Blob, fileName: string): string {
    // For iOS Safari, we need special handling
    if (this.deviceInfo.isIOS && this.browserInfo.isSafari) {
      return this.downloadForIOSSafari(blob, fileName);
    }

    // For Android, try the standard method first
    if (this.deviceInfo.isAndroid) {
      return this.downloadWithLinkElement(blob, fileName);
    }

    // General mobile fallback
    return this.downloadUniversalFallback(blob, fileName);
  }

  /**
   * Special handling for iOS Safari
   */
  private downloadForIOSSafari(blob: Blob, fileName: string): string {
    const reader = new FileReader();
    reader.onload = () => {
      const url = reader.result as string;
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.target = '_blank';

      // Create a click event
      const event = new MouseEvent('click', {
        view: window,
        bubbles: true,
        cancelable: true
      });

      link.dispatchEvent(event);
    };
    reader.readAsDataURL(blob);

    this.logger.info('✅ Download triggered using iOS Safari method');
    return fileName;
  }

  /**
   * Universal fallback download method
   */
  private downloadUniversalFallback(blob: Blob, fileName: string): string {
    try {
      const url = URL.createObjectURL(blob);

      // Try window.open as fallback
      const newWindow = window.open(url, '_blank');
      if (newWindow) {
        newWindow.document.title = fileName;
        this.toastService.info(`📁 ${fileName} opened in new tab. Right-click and save to download.`);
      } else {
        // If popup blocked, show instructions
        this.toastService.warning('Popup blocked. Please allow popups and try again.');
        this.toastService.info('💡 Alternative: Copy the download link and paste in new tab');
      }

      // Clean up after delay
      setTimeout(() => URL.revokeObjectURL(url), 5000);

      this.logger.info('✅ Download triggered using universal fallback method');
      return fileName;

    } catch (error) {
      this.logger.error('Universal fallback failed:', error);
      this.toastService.error('Download failed. Please try again or contact support.');
      return fileName;
    }
  }

  /**
   * FEATURE: Clone repository in VS Code using repository metadata from SSE events
   * Uses the vscode:// protocol to open the repository directly in VS Code
   */
  cloneInVSCode(): void {
    try {
      const repositoryMetadata = this.generationStateService.getCurrentRepositoryMetadata();

      if (!repositoryMetadata || !repositoryMetadata.cloneUrl) {
        this.logger.warn('⚠️ No repository metadata available for VS Code clone');
        this.toastService.warning('Repository information not available. Please wait for generation to complete.');
        return;
      }

      const { cloneUrl, repositoryName } = repositoryMetadata;
      this.logger.info('🔗 Attempting to clone repository in VS Code', { cloneUrl, repositoryName });

      // Use VS Code protocol to clone repository
      const vscodeCloneUrl = `vscode://vscode.git/clone?url=${encodeURIComponent(cloneUrl)}`;

      // Attempt to open in VS Code
      const opened = window.open(vscodeCloneUrl, '_blank');

      if (opened) {
        this.logger.info('✅ VS Code clone URL opened successfully');
        this.toastService.success(`Opening ${repositoryName} in VS Code...`);
      } else {
        this.logger.warn('⚠️ Failed to open VS Code clone URL - popup blocked or VS Code not installed');
        this.handleVSCodeCloneFallback(cloneUrl, repositoryName);
      }

    } catch (error) {
      this.logger.error('❌ Error in cloneInVSCode', error);
      this.toastService.error('Failed to open repository in VS Code. Please try again.');
    }
  }

  /**
   * Handle fallback when VS Code clone fails
   */
  private handleVSCodeCloneFallback(cloneUrl: string, repositoryName: string): void {
    // Copy clone URL to clipboard as fallback
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(cloneUrl).then(() => {
        this.toastService.info(`Repository URL copied to clipboard: ${cloneUrl}`);
        this.toastService.info('💡 Paste this URL in VS Code: View → Command Palette → Git: Clone');
      }).catch(() => {
        this.showManualCloneInstructions(cloneUrl, repositoryName);
      });
    } else {
      this.showManualCloneInstructions(cloneUrl, repositoryName);
    }
  }

  /**
   * Show manual clone instructions when all else fails
   */
  private showManualCloneInstructions(cloneUrl: string, repositoryName: string): void {
    this.toastService.info(`Manual clone: ${cloneUrl}`);
    this.toastService.info('💡 Copy the URL above and use Git clone or VS Code Git: Clone command');
    this.logger.info('📋 Showing manual clone instructions', { cloneUrl, repositoryName });
  }

  /**
   * Check if repository metadata is available for cloning
   */
  isRepositoryAvailableForClone(): boolean {
    const metadata = this.generationStateService.getCurrentRepositoryMetadata();
    return !!(metadata && metadata.cloneUrl);
  }

  /**
   * Get current repository name for display purposes
   */
  getCurrentRepositoryName(): string {
    const metadata = this.generationStateService.getCurrentRepositoryMetadata();
    return metadata?.repositoryName || 'Generated Project';
  }
}
