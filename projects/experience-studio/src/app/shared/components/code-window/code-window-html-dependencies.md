# Code Window Component HTML Dependencies Analysis

## Overview
This document lists all methods, properties, observables, and dependencies used in the `code-window.component.html` template. Use this for optimization, refactoring, and ensuring no breaking changes when modifying the TypeScript component.

## 🔍 **Observable Properties (Async Pipes)**

### Core State Observables
- `currentTheme | async` - Theme management (light/dark)
- `currentView | async` - Active tab/view state
- `isHistoryActive | async` - History panel state
- `isArtifactsActive | async` - Artifacts tab state
- `isLeftPanelCollapsed | async` - Left panel collapse state
- `minWidth | async` - Panel minimum width
- `shouldHideProjectName | async` - Project name visibility

### UI Design Mode Observables
- `isUIDesignMode | async` - UI design mode state
- `showUIDesignOverviewTab | async` - Overview tab visibility
- `uiDesignNodes | async` - UI design nodes array
- `uiDesignLoadingNodes | async` - Loading nodes for regeneration
- `showCanvasTooltip | async` - Canvas tooltip visibility
- `uiDesignPages | async` - UI design pages array
- `currentUIDesignPageIndex | async` - Current page index

### Preview & Code State Observables
- `previewError | async` - Preview error state
- `errorDescription | async` - Error description text
- `errorTerminalOutput | async` - Terminal error output
- `deployedUrl | async` - Deployed application URL
- `files | async` - Code files array
- `isIframeReady | async` - Iframe ready state
- `isUrlValidated | async` - URL validation state
- `isUrlAvailable | async` - URL availability state
- `urlValidationError | async` - URL validation error
- `previewTabName | async` - Preview tab name
- `regenerationInProgress$ | async` - Regeneration state
- `isRegenerationInProgress$ | async` - Regeneration progress
- `isExperienceStudioModalOpen | async` - Export modal state

### Design Token Observables
- `designTokenEditState | async` - Design token edit state

## 🎯 **Public Methods (Event Handlers)**

### Navigation & Panel Control
- `navigateToHome()` - Navigate to home page
- `toggleLeftPanel()` - Toggle left panel visibility
- `toggleHistoryView()` - Toggle history view

### Tab Management
- `onTabClick(tab: string)` - Handle tab clicks
- `handleEnhancedPreviewTabClick()` - Enhanced preview tab handler
- `handleEnhancedCodeTabClick()` - Enhanced code tab handler

### Chat & Interaction
- `handleIconClick($event)` - Handle icon clicks
- `handleUIDesignPromptSubmission()` - Handle prompt submission
- `handleRegenerationPayload($event)` - Handle regeneration
- `handleUserMessageData($event)` - Handle user messages
- `onRetryClick()` - Handle retry actions

### UI Design Canvas
- `onUIDesignPageChange($event)` - Page change handler
- `onUIDesignFullscreenRequest($event)` - Fullscreen request
- `onCanvasMouseDown($event)` - Canvas mouse down
- `onCanvasMouseMove($event)` - Canvas mouse move
- `onCanvasMouseUp($event)` - Canvas mouse up
- `onCanvasWheel($event)` - Canvas wheel events
- `onUIDesignNodeSelect(node, $event)` - Node selection
- `onUIDesignNodeDoubleClick(node)` - Node double click

### Canvas Controls
- `zoomOutCanvas()` - Zoom out canvas
- `zoomInCanvas()` - Zoom in canvas
- `resetCanvasView()` - Reset canvas view
- `fitCanvasToView()` - Fit canvas to view
- `selectAllNodes()` - Select all nodes
- `clearAllSelection()` - Clear node selection

### Preview & Code
- `openPreviewInNewTab()` - Open preview in new tab
- `onEditButtonClick()` - Edit button handler
- `toggleExportModal()` - Toggle export modal
- `onIframeLoad($event)` - Iframe load handler
- `onIframeError($event)` - Iframe error handler

### Artifacts & Logs
- `selectArtifactFile(file)` - Select artifact file
- `toggleCodeExpansion(log)` - Toggle code expansion
- `toggleLogsFooter()` - Toggle logs footer
- `scrollToBottom()` - Scroll logs to bottom
- `onTokenValueChange($event, tokenId)` - Token value change

## 📊 **Public Properties**

### Basic Properties
- `projectName` - Project name string
- `isProjectNameLoading` - Project name loading state
- `selectedImageDataUri` - Selected image data URI
- `lightPrompt` - Chat prompt text
- `lightMessages` - Chat messages array
- `rightIcons` - Right panel icons
- `projectId` - Project ID
- `jobId` - Job ID
- `codeRegenerationProgressDescription` - Regeneration progress
- `currentProgressState` - Current progress state
- `lastProgressDescription` - Last progress description
- `pollingStatus` - Polling status
- `loadingMessages` - Loading messages array

### Artifacts & Files
- `artifactsData` - Artifacts data array
- `selectedArtifactFile` - Selected artifact file
- `layoutAnalyzedData` - Layout analyzed data
- `designSystemData` - Design system data
- `formattedLogMessages` - Formatted log messages
- `isArtifactsTabEnabled` - Artifacts tab enabled state
- `isArtifactsTabEnabledWithLogs` - Artifacts with logs enabled
- `hasLogs` - Has logs boolean
- `isStreamingLogs` - Streaming logs state
- `isLogsFooterExpanded` - Logs footer expanded state

### UI State Properties
- `isCodeGenerationComplete` - Code generation complete state
- `isElementSelectionMode` - Element selection mode
- `isViewingSeedProjectTemplate` - Viewing seed template
- `isNewPreviewTabEnabled` - New preview tab enabled
- `currentPreviewTabState` - Preview tab state object
- `currentCodeTabState` - Code tab state object
- `urlSafe` - Safe URL for iframe
- `layoutData` - Layout data array
- `layoutMapping` - Layout mapping object
- `isTypingLog` - Typing log state
- `currentLogIndex` - Current log index

## 🔧 **Computed/Getter Methods**

### State Getters
- `getPromptBarPlaceholder()` - Get prompt placeholder text
- `shouldShowStepper()` - Should show stepper
- `getPromptBarEnabledState()` - Get prompt bar enabled state
- `shouldShowUIDesignLoadingIndicator()` - UI design loading indicator
- `shouldShowCodeGenerationLoadingIndicator()` - Code generation loading
- `getArtifactsTabTooltip()` - Artifacts tab tooltip
- `getCanvasZoomPercentage()` - Canvas zoom percentage
- `getCanvasTransformStyle()` - Canvas transform style
- `getSelectedNodesCount()` - Selected nodes count

### Canvas State Checkers
- `isCanvasAtMinZoom()` - Is canvas at minimum zoom
- `isCanvasAtMaxZoom()` - Is canvas at maximum zoom
- `isNodeSelectedForEditing(nodeId)` - Is node selected for editing

### Artifact & File Helpers
- `getFilteredArtifactsData()` - Get filtered artifacts
- `getFileIconClass(type)` - Get file icon class
- `getArtifactVisibleContent(name)` - Get artifact visible content
- `shouldShowAnalyzingLayout()` - Should show analyzing layout
- `getDefaultLayoutKeyForAnalyzing()` - Get default layout key
- `shouldShowDesignTokenLoadingAnimation()` - Show design token loading
- `hasActualDesignTokensFromPolling()` - Has actual design tokens
- `getTokensByCategory(category)` - Get tokens by category
- `getDesignTokenEditButtonText()` - Get edit button text
- `areDesignTokenInputsDisabled()` - Are inputs disabled

### Log & Code Helpers
- `isCodeExpanded(logId)` - Is code expanded
- `formatCodeForDisplay(content)` - Format code for display
- `getLogClass(log)` - Get log CSS class

### Layout & Tracking Helpers
- `trackByUIDesignNode(index, node)` - Track by function for nodes
- `trackByLayoutId(index, layout)` - Track by function for layouts
- `trackByLogIndex(index, log)` - Track by function for logs
- `getLayoutForPageIndex(index)` - Get layout for page index
- `getPageTitle(index, layout)` - Get page title

## 🎨 **CSS Classes & Styling**

### Dynamic CSS Classes
- Theme-based classes: `(currentTheme | async) + '-theme'`
- State-based classes: `[class.active]`, `[class.disabled]`, `[class.loading]`
- Error classes: `[class.error-tab]`, `[class.enabled]`
- Canvas classes: `[class.dragging]`, `[class.loading]`, `[class.expanded]`

## 🔗 **External Dependencies**

### Child Components Used
- `app-chat-window` - Chat interface component
- `app-mobile-frame` - Mobile frame component
- `app-canvas-info` - Canvas info component
- `app-loading-animation` - Loading animation component
- `app-code-viewer` - Code viewer component
- `app-error-page` - Error page component
- `app-analyzing-layout-animation` - Layout analysis animation
- `app-layout-identified-animation` - Layout identified animation
- `app-analyzing-design-tokens-animation` - Design tokens animation
- `markdown` - Markdown renderer component

### AWE Framework Components
- `awe-splitscreen` - Split screen layout
- `awe-leftpanel` / `awe-rightpanel` - Panel components
- `awe-icons` - Icon components

### Services Used (Injected)
- `wireframeGenerationStateService` - Wireframe generation state
- `uiDesignVisualFeedbackService` - UI design visual feedback

## 📝 **Notes for Optimization**

1. **High-frequency methods**: Canvas event handlers, track-by functions
2. **Heavy computations**: Layout calculations, code formatting
3. **State synchronization**: Multiple observables with async pipes
4. **Memory considerations**: Large arrays (logs, nodes, artifacts)
5. **Performance bottlenecks**: Frequent DOM updates, animations

## ⚠️ **Critical Dependencies**

These methods/properties are essential and should not be removed:
- All navigation methods (`navigateToHome`, `toggleLeftPanel`)
- All event handlers (click, mouse, keyboard events)
- All async pipe observables
- All track-by functions for *ngFor loops
- All state getter methods used in template conditions

## 🏗️ **Template Structure Analysis**

### Main Layout Sections
1. **Split Screen Container** (`awe-splitscreen`)
   - Left Panel: Chat window and history
   - Right Panel: Tabs and content views

2. **Left Panel Content**
   - Chat window (`app-chat-window`)
   - History container (conditional)

3. **Right Panel Header**
   - Navigation icons
   - Tab controls (Preview, Code, Artifacts, Export)
   - Action buttons (Edit, Fullscreen, Export)

4. **Right Panel Content Views**
   - Editor View: Loading animation + Code viewer
   - Overview View: Mobile frame (UI Design mode)
   - Preview View: Canvas (UI Design) or Iframe (Standard)
   - Artifacts View: File explorer + Content viewer

### Conditional Rendering Patterns
- `*ngIf="(currentView | async) === 'view-name'"` - View switching
- `*ngIf="isUIDesignMode | async"` - Mode-specific content
- `*ngIf="!isCodeGenerationComplete"` - Loading states
- `*ngIf="previewError | async"` - Error states

## 🚀 **Optimization Recommendations**

### 1. OnPush Change Detection
```typescript
@Component({
  changeDetection: ChangeDetectionStrategy.OnPush
})
```
**Impact**: Reduces unnecessary change detection cycles
**Required**: All async pipes and manual `cdr.markForCheck()` calls

### 2. TrackBy Functions Optimization
Current trackBy functions are essential for performance:
- `trackByUIDesignNode` - For UI design nodes
- `trackByLayoutId` - For layout data
- `trackByLogIndex` - For log messages

### 3. Lazy Loading Opportunities
- **Code viewer**: Load only when Code tab is active
- **Canvas components**: Load only in UI Design mode
- **Artifact viewers**: Load only when Artifacts tab is active

### 4. Memory Management
- **Large arrays**: `formattedLogMessages`, `uiDesignNodes`, `artifactsData`
- **Image optimization**: Layout images, artifact images
- **DOM cleanup**: Remove unused nodes, clear intervals/timeouts

### 5. Bundle Size Optimization
- **Conditional imports**: Load heavy components only when needed
- **Tree shaking**: Remove unused methods from services
- **Code splitting**: Separate UI Design mode from standard mode

## 📊 **Performance Metrics to Monitor**

### Template Complexity
- **Total async pipes**: ~30+ observables
- **Conditional blocks**: ~50+ *ngIf statements
- **Event handlers**: ~40+ click/mouse/keyboard events
- **Child components**: ~15+ different components

### Heavy Operations
1. **Canvas rendering**: Mouse events, zoom, pan, selection
2. **Log streaming**: Real-time updates, formatting, expansion
3. **Code highlighting**: Syntax highlighting, line numbers
4. **Image loading**: Layout images, artifact images

## 🔧 **Refactoring Opportunities**

### 1. Extract Sub-Components
- **Canvas Controls**: Zoom, pan, selection controls
- **Artifacts File Explorer**: File tree and content viewer
- **Logs Viewer**: Log formatting and expansion
- **Tab Header**: Tab management and state

### 2. Service Extraction
- **Canvas State Service**: Zoom, pan, selection state
- **Artifacts State Service**: File selection, content management
- **Logs State Service**: Log formatting, expansion state

### 3. State Management
- **Centralized state**: Use NgRx or Akita for complex state
- **Local state**: Keep simple UI state in component
- **Computed properties**: Use signals for derived state

## 📋 **Testing Considerations**

### Critical User Flows
1. **Tab switching**: Preview ↔ Code ↔ Artifacts
2. **Canvas interaction**: Zoom, pan, node selection
3. **Code generation**: Loading → Preview → Code
4. **Error handling**: Failed states, retry actions
5. **Regeneration**: Edit flow, progress tracking

### Performance Tests
- **Initial load time**: Component initialization
- **Tab switch time**: View transitions
- **Canvas responsiveness**: Mouse/touch interactions
- **Memory usage**: Long-running sessions
- **Bundle size**: Lazy loading effectiveness

## 🎯 **Migration Strategy**

### Phase 1: Immediate Optimizations
- Add OnPush change detection
- Optimize trackBy functions
- Add lazy loading for heavy components

### Phase 2: Component Extraction
- Extract canvas controls
- Extract artifacts viewer
- Extract logs viewer

### Phase 3: State Management
- Implement centralized state
- Add computed properties
- Optimize async operations

### Phase 4: Advanced Optimizations
- Implement virtual scrolling for logs
- Add image lazy loading
- Optimize bundle splitting
