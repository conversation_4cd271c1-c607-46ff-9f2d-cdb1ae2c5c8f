import { Injectable, signal, computed, inject } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from '../../../services/toast.service';

export interface UIDesignNode {
  data?: {
    rawContent?: string;
    displayTitle?: string;
    title?: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignCodeViewerService {
  private readonly toastService = inject(ToastService);

  // Private state management
  private readonly isUIDesignCodeViewerOpen$ = new BehaviorSubject<boolean>(false);
  private readonly selectedUIDesignNode$ = new BehaviorSubject<UIDesignNode | null>(null);

  // Cache variables for performance optimization
  private _codeLines: string[] = [];
  private _lastProcessedContent: string = '';

  // Reactive state using Angular 19+ Signals
  private readonly codeLineNumbersSignal = signal<number[]>([]);
  private readonly highlightedCodeSignal = signal<string>('');
  private readonly codeFileNameSignal = signal<string>('');

  // Public observables for template compatibility
  readonly isUIDesignCodeViewerOpen = this.isUIDesignCodeViewerOpen$.asObservable();

  // Public signals for reactive access
  readonly codeLineNumbers = this.codeLineNumbersSignal.asReadonly();
  readonly highlightedCode = this.highlightedCodeSignal.asReadonly();
  readonly codeFileName = this.codeFileNameSignal.asReadonly();

  // Computed properties for additional functionality
  readonly hasCodeContent = computed(() => this.highlightedCodeSignal().length > 0);
  readonly lineCount = computed(() => this.codeLineNumbersSignal().length);

  constructor() {
    // Set up reactive subscriptions with proper cleanup
    this.setupReactiveSubscriptions();
  }

  private setupReactiveSubscriptions(): void {
    // Subscribe to selected node changes and update code data automatically
    this.selectedUIDesignNode$
      .pipe(takeUntilDestroyed())
      .subscribe(node => {
        if (node && this.isUIDesignCodeViewerOpen$.value) {
          this.updateCodeData(node);
        }
      });
  }

  /**
   * Open the UI Design code viewer modal
   */
  openUIDesignCodeViewer(selectedNode: UIDesignNode | null): void {
    if (!selectedNode?.data?.rawContent) {
      this.toastService.warning('No code content available to view');
      return;
    }

    this.selectedUIDesignNode$.next(selectedNode);
    this.updateCodeData(selectedNode);
    this.isUIDesignCodeViewerOpen$.next(true);
  }

  /**
   * Close the UI Design code viewer modal and clear cache
   */
  closeUIDesignCodeViewer(): void {
    this.isUIDesignCodeViewerOpen$.next(false);

    // Clear cache for memory optimization
    this._codeLines = [];
    this._lastProcessedContent = '';

    // Clear reactive state
    this.codeLineNumbersSignal.set([]);
    this.highlightedCodeSignal.set('');
    this.codeFileNameSignal.set('');

    // Clear selected node
    this.selectedUIDesignNode$.next(null);
  }

  /**
   * Update code data from selected node
   */
  private updateCodeData(selectedNode: UIDesignNode): void {
    if (!selectedNode?.data?.rawContent) {
      this.codeLineNumbersSignal.set([]);
      this.highlightedCodeSignal.set('');
      this.codeFileNameSignal.set('ui-design.html');
      return;
    }

    const currentContent = selectedNode.data.rawContent;

    // Only process if content has changed (performance optimization)
    if (this._lastProcessedContent !== currentContent) {
      this._codeLines = currentContent.split('\n');
      const lineNumbers = Array.from({ length: this._codeLines.length }, (_, i) => i + 1);
      const highlightedCode = this.generateHighlightedCode(currentContent);

      const title = selectedNode.data.displayTitle || selectedNode.data.title || 'ui-design';
      const fileName = `${title.toLowerCase().replace(/[^a-z0-9]/g, '-')}.html`;

      // Update reactive state
      this.codeLineNumbersSignal.set(lineNumbers);
      this.highlightedCodeSignal.set(highlightedCode);
      this.codeFileNameSignal.set(fileName);

      this._lastProcessedContent = currentContent;
    }
  }

  /**
   * Generate syntax-highlighted HTML code
   */
  private generateHighlightedCode(content: string): string {
    if (!content) {
      return '';
    }

    let code = content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');

    // Apply syntax highlighting with optimized regex
    code = code
      // HTML tags
      .replace(/(&lt;\/?)([a-zA-Z][a-zA-Z0-9]*)(.*?)(&gt;)/g,
        '<span class="html-tag">$1</span><span class="html-tag-name">$2</span><span class="html-attributes">$3</span><span class="html-tag">$4</span>')
      // Attributes
      .replace(/(\w+)(=)(&quot;[^&]*&quot;)/g,
        '<span class="html-attr-name">$1</span><span class="html-operator">$2</span><span class="html-attr-value">$3</span>')
      // Comments
      .replace(/(&lt;!--.*?--&gt;)/g, '<span class="html-comment">$1</span>')
      // Doctype
      .replace(/(&lt;!DOCTYPE.*?&gt;)/gi, '<span class="html-doctype">$1</span>');

    return code;
  }

  /**
   * TrackBy function for line numbers optimization
   */
  trackByLineNumber(_index: number, lineNumber: number): number {
    return lineNumber;
  }

  /**
   * Handle code content scroll to sync line numbers
   */
  onCodeScroll(event: Event): void {
    const codeContent = event.target as HTMLElement;
    const lineNumbers = codeContent.parentElement?.querySelector('.line-numbers') as HTMLElement;

    if (lineNumbers) {
      lineNumbers.scrollTop = codeContent.scrollTop;
    }
  }

  /**
   * Get current code file name (for backward compatibility)
   */
  getCodeFileName(): string {
    return this.codeFileNameSignal();
  }

  // Getters for template compatibility (maintaining exact same interface)
  get codeLineNumbersValue(): number[] {
    return this.codeLineNumbersSignal();
  }

  get highlightedCodeValue(): string {
    return this.highlightedCodeSignal();
  }

  get codeFileNameValue(): string {
    return this.codeFileNameSignal();
  }

  // State getters
  get isUIDesignCodeViewerOpenValue(): boolean {
    return this.isUIDesignCodeViewerOpen$.value;
  }
}
