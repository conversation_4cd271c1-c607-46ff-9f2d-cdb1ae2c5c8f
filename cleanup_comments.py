#!/usr/bin/env python3
"""
Script to remove all comments and JSDoc documentation from TypeScript files.
This script removes:
1. Single-line comments (// ...)
2. Multi-line comments (/* ... */)
3. JSDoc documentation (/** ... */)
"""

import re
import sys

def remove_comments_and_jsdoc(content):
    """
    Remove all comments and JSDoc documentation from TypeScript content.
    Preserves string literals and template literals.
    """
    lines = content.split('\n')
    result_lines = []
    in_multiline_comment = False
    in_jsdoc = False
    in_string = False
    in_template_literal = False
    string_delimiter = None
    
    i = 0
    while i < len(lines):
        line = lines[i]
        processed_line = ""
        j = 0
        
        while j < len(line):
            char = line[j]
            next_char = line[j + 1] if j + 1 < len(line) else None
            
            # Handle string literals
            if not in_multiline_comment and not in_jsdoc:
                if char in ['"', "'", '`'] and not in_string:
                    in_string = True
                    string_delimiter = char
                    if char == '`':
                        in_template_literal = True
                    processed_line += char
                    j += 1
                    continue
                elif in_string and char == string_delimiter:
                    # Check if it's escaped
                    escape_count = 0
                    k = j - 1
                    while k >= 0 and line[k] == '\\':
                        escape_count += 1
                        k -= 1
                    
                    if escape_count % 2 == 0:  # Not escaped
                        in_string = False
                        in_template_literal = False
                        string_delimiter = None
                    processed_line += char
                    j += 1
                    continue
                elif in_string:
                    processed_line += char
                    j += 1
                    continue
            
            # Handle multi-line comments and JSDoc
            if in_multiline_comment or in_jsdoc:
                if char == '*' and next_char == '/':
                    in_multiline_comment = False
                    in_jsdoc = False
                    j += 2
                    continue
                else:
                    j += 1
                    continue
            
            # Check for comment start
            if char == '/' and next_char == '*':
                # Check if it's JSDoc
                third_char = line[j + 2] if j + 2 < len(line) else None
                if third_char == '*':
                    in_jsdoc = True
                else:
                    in_multiline_comment = True
                j += 2
                continue
            elif char == '/' and next_char == '/':
                # Single-line comment - skip rest of line
                break
            else:
                processed_line += char
                j += 1
        
        # Only add non-empty lines or lines that aren't just whitespace
        if processed_line.strip() or (not in_multiline_comment and not in_jsdoc):
            result_lines.append(processed_line.rstrip())
        
        i += 1
    
    # Remove empty lines that were left by comment removal
    final_lines = []
    for line in result_lines:
        if line.strip():  # Only keep non-empty lines
            final_lines.append(line)
        elif final_lines and final_lines[-1].strip():  # Keep one empty line after content
            final_lines.append('')
    
    return '\n'.join(final_lines)

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 cleanup_comments.py <typescript_file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        cleaned_content = remove_comments_and_jsdoc(content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print(f"Successfully cleaned comments and JSDoc from {file_path}")
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
