#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to remove all logger calls from TypeScript files while preserving code structure.
This script carefully removes logger method calls without breaking surrounding syntax.
"""

import re
import sys

def remove_logger_calls(content):
    """
    Remove all logger method calls from the content while preserving code structure.
    Handles both single-line and multi-line logger calls with proper bracket matching.
    """
    lines = content.split('\n')
    result_lines = []
    i = 0

    while i < len(lines):
        line = lines[i]

        # Check if this line contains a logger call
        if 'this.logger.' in line:
            # Extract the indentation to preserve it for any following code
            indentation = len(line) - len(line.lstrip())

            # Check if it's a complete logger call on one line
            if line.strip().endswith(');'):
                # Single line logger call - skip it entirely
                i += 1
                continue
            elif line.strip().endswith(';') and not '(' in line.split('this.logger.')[1]:
                # Simple assignment or property access - skip it
                i += 1
                continue
            else:
                # Multi-line logger call - find the end by counting parentheses
                paren_count = line.count('(') - line.count(')')
                j = i + 1

                while j < len(lines) and paren_count > 0:
                    next_line = lines[j]
                    paren_count += next_line.count('(') - next_line.count(')')
                    j += 1

                # Skip all lines from i to j-1 (inclusive)
                i = j
                continue
        else:
            # Not a logger call, keep the line
            result_lines.append(line)
            i += 1

    return '\n'.join(result_lines)

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 cleanup_logger.py <typescript_file>")
        sys.exit(1)

    file_path = sys.argv[1]

    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Remove logger calls
        cleaned_content = remove_logger_calls(content)

        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)

        print(f"Successfully cleaned logger calls from {file_path}")

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
